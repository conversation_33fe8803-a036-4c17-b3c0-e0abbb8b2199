#!/usr/bin/env python3
"""
Simple test to verify form submission and success message functionality.
"""

import requests
import re
from bs4 import BeautifulSoup

def test_form_submission():
    """Test form submission with minimal data."""

    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"

    print("🧪 Testing Success Message Display")
    print("=" * 50)

    # Create a session to maintain cookies
    session = requests.Session()

    # Step 1: Load the configuration page
    print("1. Loading configuration page...")
    response = session.get(config_url)

    if response.status_code != 200:
        print(f"❌ Failed to load page. Status: {response.status_code}")
        return False

    print(f"✅ Page loaded successfully (Status: {response.status_code})")

    # Step 2: Check for success message in the response (should appear due to test message in GET method)
    soup = BeautifulSoup(response.content, 'html.parser')
    success_div = soup.find('div', class_='rounded-md bg-green-50 p-4 mb-6')

    if success_div:
        message_text = success_div.get_text()
        expected_text = "Configuration widget has been saved successfully"

        if expected_text in message_text:
            print(f"✅ Success message found: '{expected_text}'")
            print("🎉 SUCCESS NOTIFICATION DISPLAY TEST PASSED!")
            return True
        else:
            print(f"❌ Expected message not found. Got: '{message_text}'")
            return False
    else:
        print("❌ Success notification div not found")

        # Debug: Look for any message-related elements
        message_divs = soup.find_all('div', class_=re.compile(r'.*green.*|.*success.*|.*message.*'))
        if message_divs:
            print(f"Found {len(message_divs)} potential message divs:")
            for i, div in enumerate(message_divs):
                print(f"  {i+1}. {div.get('class', [])} - {div.get_text()[:100]}...")
        else:
            print("No message-related divs found")

        return False

def test_form_submission_old():
    """Test form submission with minimal data."""

    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"

    print("🧪 Testing Form Submission")
    print("=" * 50)

    # Create a session to maintain cookies
    session = requests.Session()

    # Step 1: Load the configuration page
    print("1. Loading configuration page...")
    response = session.get(config_url)

    if response.status_code != 200:
        print(f"❌ Failed to load page. Status: {response.status_code}")
        return False

    print(f"✅ Page loaded successfully (Status: {response.status_code})")

    # Step 2: Parse the form and extract CSRF token
    soup = BeautifulSoup(response.content, 'html.parser')
    form = soup.find('form', class_='config-form')
    
    if not form:
        print("❌ Configuration form not found")
        return False
    
    csrf_token = form.find('input', {'name': 'csrfmiddlewaretoken'})
    if not csrf_token:
        print("❌ CSRF token not found")
        return False
    
    csrf_value = csrf_token.get('value')
    print(f"✅ CSRF token found: {csrf_value[:20]}...")
    
    # Step 3: Extract all form field values to create valid submission
    form_data = {
        'csrfmiddlewaretoken': csrf_value,
    }

    # Extract all input field values
    for input_field in form.find_all('input'):
        name = input_field.get('name')
        if name and name != 'csrfmiddlewaretoken':
            input_type = input_field.get('type', 'text')
            if input_type == 'checkbox':
                if input_field.get('checked'):
                    form_data[name] = input_field.get('value', 'on')
            elif input_type == 'radio':
                if input_field.get('checked'):
                    form_data[name] = input_field.get('value', '')
            else:
                form_data[name] = input_field.get('value', '')

    # Extract all select field values
    for select_field in form.find_all('select'):
        name = select_field.get('name')
        if name:
            selected_option = select_field.find('option', selected=True)
            if selected_option:
                form_data[name] = selected_option.get('value', '')
            else:
                # Use first option if none selected
                first_option = select_field.find('option')
                if first_option:
                    form_data[name] = first_option.get('value', '')

    # Extract all textarea field values
    for textarea_field in form.find_all('textarea'):
        name = textarea_field.get('name')
        if name:
            form_data[name] = textarea_field.get_text()

    print(f"Form data extracted: {len(form_data)} fields")
    for key, value in form_data.items():
        if key != 'csrfmiddlewaretoken':
            print(f"  {key}: {value}")

    # Add missing required fields that might not be in the form
    if 'theme-theme_name' not in form_data:
        form_data['theme-theme_name'] = 'default'  # Use default theme

    # Make a small change to ensure form processing
    if 'config-description' in form_data:
        form_data['config-description'] = 'Updated by test script'
    
    print("2. Submitting form with extracted data...")
    
    # Step 4: Submit the form
    response = session.post(config_url, data=form_data, allow_redirects=False)
    
    print(f"Response status: {response.status_code}")
    print(f"Response headers: {dict(response.headers)}")
    
    if response.status_code == 302:
        print(f"✅ Form submitted with redirect to: {response.headers.get('Location')}")
        
        # Follow the redirect
        redirect_response = session.get(response.headers.get('Location'))
        print(f"Redirect response status: {redirect_response.status_code}")
        
        # Check for success message in the redirected page
        soup = BeautifulSoup(redirect_response.content, 'html.parser')
        success_div = soup.find('div', class_='rounded-md bg-green-50 p-4 mb-6')
        
        if success_div:
            message_text = success_div.get_text()
            print(f"✅ Success message found: '{message_text.strip()}'")
            return True
        else:
            print("❌ Success message not found in redirected page")
            return False
    
    elif response.status_code == 200:
        print("Form submission returned 200 (no redirect)")
        
        # Check for success message in the same page
        soup = BeautifulSoup(response.content, 'html.parser')
        success_div = soup.find('div', class_='rounded-md bg-green-50 p-4 mb-6')
        
        if success_div:
            message_text = success_div.get_text()
            print(f"✅ Success message found: '{message_text.strip()}'")
            return True
        else:
            print("❌ Success message not found")
            
            # Check for form errors
            error_divs = soup.find_all('div', class_='alert-danger')
            if error_divs:
                print("Form errors found:")
                for error_div in error_divs:
                    print(f"  - {error_div.get_text().strip()}")
            
            return False
    
    else:
        print(f"❌ Unexpected response status: {response.status_code}")
        return False

if __name__ == "__main__":
    print("Testing Form Submission for Finder-v2 Widget")
    print("=" * 60)
    
    try:
        success = test_form_submission()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 FORM SUBMISSION TEST PASSED!")
        else:
            print("❌ FORM SUBMISSION TEST FAILED!")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
