# Test Suite Documentation

This directory contains the test suite for the wheel-size-services project, organized by functionality and component.

## Directory Structure

```
tests/
├── README.md                           # This file
├── verify_production_csrf.py           # Production CSRF configuration verification
└── widget/
    ├── test_widget_csrf_security.py    # Widget CSRF security validation
    ├── test-calc-widget.html           # Calc widget JavaScript compatibility test
    └── test-calc-widget-direct.html    # Direct calc widget JavaScript fix verification
```

## Test Categories

### Widget Tests (`widget/`)

**Purpose:** Validate widget functionality, security, and cross-domain operation.

**Key Tests:**
- **CSRF Security Validation** - Comprehensive security test suite
- **Cross-Domain Functionality** - Widget operation on client domains
- **API Endpoint Testing** - Widget API proxy functionality
- **JavaScript Compatibility** - Widget JavaScript error detection and debugging
- **Calc Widget Testing** - Specific testing for calculator widget functionality

### Production Tests (`tests/`)

**Purpose:** Validate production configuration and deployment readiness.

**Key Tests:**
- **Production CSRF Verification** - Validate CSRF settings for production deployment

## Running Tests

### All Widget Tests

```bash
# Run from project root
python tests/widget/test_widget_csrf_security.py
```

### Individual Test Files

**Widget CSRF Security Tests:**
```bash
python tests/widget/test_widget_csrf_security.py
```

**Production CSRF Verification:**
```bash
python tests/verify_production_csrf.py
```

**Widget HTML Tests:**
```bash
# Open in browser for interactive testing
open tests/widget/test-calc-widget.html
open tests/widget/test-calc-widget-direct.html
```

**Expected Output:**
```
🔒 Widget CSRF Security Test Suite
==================================================
Test Results: 10 passed, 0 failed
🎉 ALL TESTS PASSED - Widget CSRF security is working correctly!

Security Model Validated:
✅ Cross-origin widget requests allowed with valid tokens
✅ Invalid/missing tokens blocked
✅ No security bypass through hostname spoofing
```

## Test Naming Conventions

### File Naming
- `test_[component]_[functionality].py` - Main test files
- Use lowercase with underscores
- Descriptive names indicating what is being tested

### Test Function Naming
- `test_[specific_scenario]()` - Individual test functions
- `run_[test_category]_tests()` - Test suite runners
- Clear, descriptive names explaining the test scenario

### Test Class Naming (if used)
- `Test[Component][Functionality]` - Test classes
- PascalCase naming convention
- Group related tests logically

## Test Coverage

### Widget Security Tests (`test_widget_csrf_security.py`)

**Test Scenarios:**
1. ✅ Same-origin requests with valid tokens
2. ✅ Cross-origin widget requests with valid tokens
3. ✅ Different client domains with valid tokens
4. ❌ Same-origin requests with invalid tokens (blocked)
5. ❌ Cross-origin requests with invalid tokens (blocked)
6. ❌ Cross-origin requests without tokens (blocked)
7. ✅ Malicious domains with valid tokens (allowed - widgets work cross-domain)
8. ❌ Malicious domains with invalid tokens (blocked)
9. ❌ Malicious domains without tokens (blocked)
10. 🔍 Port mismatch handling (development environment)

**Security Validation:**
- CSRF token generation and validation
- Cross-domain request handling
- Security blocking of unauthorized requests
- Port mismatch handling in development
- trusted_hostnames vulnerability demonstration

## Adding New Tests

### Creating Widget Tests

1. **Create test file in appropriate subdirectory:**
   ```bash
   touch tests/widget/test_new_functionality.py
   ```

2. **Follow the established pattern:**
   ```python
   #!/usr/bin/env python3
   """
   Test description and purpose
   """

   import sys
   import requests
   # Other imports

   def test_specific_functionality():
       """Test specific functionality with clear description"""
       # Test implementation
       pass

   def run_test_suite():
       """Main test runner"""
       # Run all tests and report results
       pass

   if __name__ == '__main__':
       success = run_test_suite()
       sys.exit(0 if success else 1)
   ```

3. **Include comprehensive documentation:**
   - Clear test descriptions
   - Expected outcomes
   - Error handling
   - Result reporting

### Test Requirements

**All tests should:**
- Be self-contained and runnable independently
- Include clear success/failure reporting
- Handle errors gracefully
- Provide meaningful output for debugging
- Follow the established naming conventions
- Include documentation explaining what is being tested

**Security tests should:**
- Test both positive and negative scenarios
- Validate security boundaries
- Include attack simulation where appropriate
- Clearly document security implications
- Provide evidence of security model validation

## Test Environment

### Prerequisites
- Docker services running (`docker-compose up -d`)
- Application accessible at `http://development.local:8000`
- Database populated with test data

### Environment Variables
Tests use the development environment configuration by default.

### Dependencies
- `requests` library for HTTP testing
- `base64` for CSRF token generation
- Standard Python libraries

## Continuous Integration

### Local Testing
Run all tests before committing:
```bash
# Widget security tests
python tests/widget/test_widget_csrf_security.py
```

### Pre-Deployment Testing
Before deploying to production:
1. Run full test suite
2. Verify all security tests pass
3. Test against staging environment
4. Validate production configuration

## Troubleshooting

### Common Issues

**Test Connection Errors:**
- Ensure Docker services are running
- Check that `development.local:8000` is accessible
- Verify database is populated

**CSRF Token Failures:**
- Check User-Agent string matches between test and server
- Verify CSRF algorithm implementation
- Enable debug logging: `WIDGET_CSRF_SETTINGS['debug_csrf_validation'] = True`

**Cross-Domain Test Failures:**
- Verify CSRF settings allow cross-domain requests
- Check that `trusted_hostnames` is not configured (security risk)
- Ensure proper token validation is enabled

### Debug Mode

Enable detailed logging for troubleshooting:
```python
# In settings
WIDGET_CSRF_SETTINGS = {
    'debug_csrf_validation': True,
}
```

View debug output:
```bash
docker logs -f ws_services 2>&1 | grep "WIDGET CSRF DEBUG"
```

## Contributing

### Adding Tests
1. Create test file in appropriate subdirectory
2. Follow naming conventions and patterns
3. Include comprehensive documentation
4. Test both success and failure scenarios
5. Update this README if adding new test categories

### Test Review Checklist
- [ ] Test file follows naming conventions
- [ ] Tests are self-contained and runnable
- [ ] Clear success/failure reporting
- [ ] Comprehensive scenario coverage
- [ ] Documentation explains test purpose
- [ ] Error handling implemented
- [ ] Security implications considered (for security tests)

---

**For more information about the project and its architecture, see [docs/README.md](../docs/README.md)**
