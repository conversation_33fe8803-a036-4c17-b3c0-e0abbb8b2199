#!/usr/bin/env python3
"""
Test script to verify that the success notification system works correctly
for the finder-v2 widget configuration page.

This script tests:
1. Loading the configuration page
2. Submitting the form with valid data
3. Verifying that the success message appears after form submission
"""

import requests
import re
from bs4 import BeautifulSoup

def test_success_notification():
    """Test that success notification appears after form submission."""
    
    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"
    
    print("🧪 Testing Success Notification System")
    print("=" * 50)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Step 1: Load the configuration page
    print("1. Loading configuration page...")
    response = session.get(config_url)
    
    if response.status_code != 200:
        print(f"❌ Failed to load page. Status: {response.status_code}")
        return False
    
    print(f"✅ Page loaded successfully (Status: {response.status_code})")
    
    # Step 2: Parse the form and extract CSRF token
    soup = BeautifulSoup(response.content, 'html.parser')
    form = soup.find('form', class_='config-form')
    
    if not form:
        print("❌ Configuration form not found")
        return False
    
    csrf_token = form.find('input', {'name': 'csrfmiddlewaretoken'})
    if not csrf_token:
        print("❌ CSRF token not found")
        return False
    
    csrf_value = csrf_token.get('value')
    print(f"✅ CSRF token found: {csrf_value[:20]}...")
    
    # Step 3: Extract existing form values and prepare minimal changes
    form_data = {
        'csrfmiddlewaretoken': csrf_value,
    }

    # Extract all existing form field values to ensure we submit valid data
    for input_field in form.find_all(['input', 'select', 'textarea']):
        name = input_field.get('name')
        if name and name != 'csrfmiddlewaretoken':
            if input_field.name == 'input':
                if input_field.get('type') == 'checkbox':
                    if input_field.get('checked'):
                        form_data[name] = input_field.get('value', 'on')
                elif input_field.get('type') == 'radio':
                    if input_field.get('checked'):
                        form_data[name] = input_field.get('value', '')
                else:
                    form_data[name] = input_field.get('value', '')
            elif input_field.name == 'select':
                # Find selected option
                selected = input_field.find('option', selected=True)
                if selected:
                    form_data[name] = selected.get('value', '')
                else:
                    # Use first option if none selected
                    first_option = input_field.find('option')
                    if first_option:
                        form_data[name] = first_option.get('value', '')
            elif input_field.name == 'textarea':
                form_data[name] = input_field.get_text()

    # Make a small change to trigger form processing
    if 'config-description' in form_data:
        form_data['config-description'] = 'Updated by test script'
    else:
        form_data['config-description'] = 'Test Description'
    
    print("2. Submitting form with test data...")
    
    # Step 4: Submit the form
    response = session.post(config_url, data=form_data)
    
    if response.status_code != 200:
        print(f"❌ Form submission failed. Status: {response.status_code}")
        return False
    
    print(f"✅ Form submitted successfully (Status: {response.status_code})")
    
    # Step 5: Check for success message in the response
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Look for the success message div
    success_div = soup.find('div', class_='rounded-md bg-green-50 p-4 mb-6')
    
    if not success_div:
        print("❌ Success notification div not found")
        print("Looking for any message divs...")
        
        # Debug: Look for any message-related elements
        message_divs = soup.find_all('div', class_=re.compile(r'.*green.*|.*success.*|.*message.*'))
        if message_divs:
            print(f"Found {len(message_divs)} potential message divs:")
            for i, div in enumerate(message_divs):
                print(f"  {i+1}. {div.get('class', [])} - {div.get_text()[:100]}...")
        else:
            print("No message-related divs found")
        
        return False
    
    # Check for the success message text
    message_text = success_div.get_text()
    expected_text = "Configuration widget has been saved successfully"
    
    if expected_text in message_text:
        print(f"✅ Success message found: '{expected_text}'")
        print("🎉 SUCCESS NOTIFICATION TEST PASSED!")
        return True
    else:
        print(f"❌ Expected message not found. Got: '{message_text}'")
        return False

def test_message_display_structure():
    """Test that the message display structure is correct."""

    print("\n🧪 Testing Message Display Structure")
    print("=" * 50)

    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"

    response = requests.get(config_url)
    soup = BeautifulSoup(response.content, 'html.parser')

    # Check if messages block exists in template
    messages_comment = soup.find(string=re.compile(r'Success/Error Messages'))

    # Also check for the actual success message div
    success_div = soup.find('div', class_='rounded-md bg-green-50 p-4 mb-6')

    if messages_comment:
        print("✅ Messages section found in template")
    else:
        print("❌ Messages section not found in template")

    if success_div:
        print("✅ Success message div found on page!")
        message_text = success_div.get_text()
        print(f"   Message content: '{message_text.strip()}'")
        return True
    else:
        print("❌ Success message div not found on page")
        # Debug: Look for any green divs
        green_divs = soup.find_all('div', class_=re.compile(r'.*green.*'))
        if green_divs:
            print(f"Found {len(green_divs)} green divs:")
            for i, div in enumerate(green_divs):
                print(f"  {i+1}. {div.get('class', [])} - {div.get_text()[:100]}...")
        return False

if __name__ == "__main__":
    print("Testing Success Notification System for Finder-v2 Widget")
    print("=" * 60)
    
    try:
        # Test 1: Check template structure
        structure_ok = test_message_display_structure()
        
        # Test 2: Test actual notification
        notification_ok = test_success_notification()
        
        print("\n" + "=" * 60)
        print("FINAL RESULTS:")
        print(f"Template Structure: {'✅ PASS' if structure_ok else '❌ FAIL'}")
        print(f"Success Notification: {'✅ PASS' if notification_ok else '❌ FAIL'}")
        
        if structure_ok and notification_ok:
            print("\n🎉 ALL TESTS PASSED! Success notification system is working correctly.")
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
