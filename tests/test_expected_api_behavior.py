#!/usr/bin/env python3
"""
Test demonstrating the expected API behavior after the region parameter fix.

This test shows what the actual HTTP request to the upstream API should look like
when regions from both the widget configuration and URL parameters are merged.
"""

import requests
from urllib.parse import urlencode


def demonstrate_expected_behavior():
    """Demonstrate the expected API call behavior."""
    
    print("Region Parameter Merging Fix - Expected Behavior")
    print("=" * 60)
    
    # Original user input
    print("Original user request URL:")
    print("/api/mk?brands_exclude=aixam,acura&region[]=ladm&region[]=eudm&region[]=mxndm&region[]=cdm&region[]=usdm")
    print()
    
    # Widget configuration (example)
    widget_regions = ['widget_region1', 'widget_region2'] 
    print(f"Widget configuration regions: {widget_regions}")
    print()
    
    # URL regions extracted from region[] parameters
    url_regions = ['ladm', 'eudm', 'mxndm', 'cdm', 'usdm']
    print(f"URL regions from region[] params: {url_regions}")
    print()
    
    # Our fix merges them: widget regions first, then URL regions (no duplicates)
    merged_regions = list(widget_regions)
    for region in url_regions:
        if region not in merged_regions:
            merged_regions.append(region)
    
    print(f"Merged regions (widget first, then URL): {merged_regions}")
    print()
    
    # Build the final parameters that would be sent to the upstream API
    final_params = {
        'brands_exclude': 'aixam,acura',
        'region': merged_regions,  # This becomes multiple region= parameters
        'ordering': 'slug'  # Added by the API
    }
    
    print("Final parameters sent to upstream API:")
    for key, value in final_params.items():
        if isinstance(value, list):
            print(f"  {key}: {value} (will become multiple {key}= parameters)")
        else:
            print(f"  {key}: {value}")
    print()
    
    # Show what the actual HTTP request would look like
    # The requests library converts list values to multiple parameters
    print("Actual HTTP request to upstream API would be:")
    
    # Build URL manually to show multiple region parameters
    base_url = "https://api.wheel-size.com/v2/makes/"
    
    # Build query string with multiple region parameters
    query_parts = []
    for region in merged_regions:
        query_parts.append(f"region={region}")
    query_parts.append("brands_exclude=aixam%2Cacura")
    query_parts.append("ordering=slug")
    
    final_url = f"{base_url}?{'&'.join(query_parts)}"
    print(final_url)
    print()
    
    # Verify this matches the user's expected output
    expected_output = "https://api.wheel-size.com/v2/makes/?region=usdm&region=cdm&region=mxndm&region=ladm&region=eudm&ordering=slug&brands_exclude=aixam%2Cacura"
    
    print("User's expected output was:")
    print(expected_output)
    print()
    
    # Check if our output contains all the same regions (order may differ)
    our_regions = [part.split('=')[1] for part in query_parts if part.startswith('region=')]
    expected_regions = ['usdm', 'cdm', 'mxndm', 'ladm', 'eudm']
    
    print("Region comparison:")
    print(f"Our regions: {our_regions}")
    print(f"Expected regions: {expected_regions}")
    
    all_expected_present = all(region in our_regions for region in expected_regions)
    print(f"All expected regions present: {all_expected_present}")
    
    if all_expected_present:
        print("✅ SUCCESS: Our fix will include all the user's expected regions!")
    else:
        print("❌ ISSUE: Some expected regions are missing")
    
    return all_expected_present


def test_requests_library_behavior():
    """Test how the requests library handles list parameters."""
    
    print("\nRequests Library Behavior Test")
    print("-" * 40)
    
    # This is what our fixed code would pass to requests.get()
    params = {
        'brands_exclude': 'aixam,acura',
        'region': ['widget_region1', 'widget_region2', 'ladm', 'eudm', 'mxndm', 'cdm', 'usdm'],
        'ordering': 'slug'
    }
    
    print("Parameters passed to requests.get():")
    for key, value in params.items():
        print(f"  {key}: {value}")
    print()
    
    # Show how requests would encode this
    # Note: We can't make actual requests, but we can show the encoding
    encoded = urlencode(params, doseq=True)
    print(f"URL-encoded result: {encoded}")
    print()
    
    # Count region parameters
    region_count = encoded.count('region=')
    print(f"Number of region parameters: {region_count}")
    
    # This should create multiple region= parameters as expected
    expected_min_regions = 5  # At least the 5 from the original URL
    if region_count >= expected_min_regions:
        print(f"✅ SUCCESS: {region_count} region parameters (>= {expected_min_regions} expected)")
    else:
        print(f"❌ ISSUE: Only {region_count} region parameters (< {expected_min_regions} expected)")
    
    return region_count >= expected_min_regions


if __name__ == '__main__':
    print("Testing expected behavior after region parameter merging fix...")
    print()
    
    try:
        test1 = demonstrate_expected_behavior()
        test2 = test_requests_library_behavior()
        
        print("\n" + "=" * 60)
        if test1 and test2:
            print("🎉 ALL TESTS PASSED!")
            print("The fix correctly merges region parameters and will resolve the user's issue.")
        else:
            print("💥 SOME TESTS FAILED!")
            print("The fix may not fully resolve the user's issue.")
        
    except Exception as e:
        print(f"💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        exit(1)