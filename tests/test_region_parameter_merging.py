#!/usr/bin/env python3
"""
Test script to verify region parameter merging in FinderV2WidgetProxyView.

This script tests the specific scenario reported by the user:
Input: `/api/mk?brands_exclude=aixam,acura&region[]=ladm&region[]=eudm&region[]=mxndm&region[]=cdm&region[]=usdm`
Expected: Region parameters should be merged properly for the upstream API call.
"""

import os
import sys
import django
from unittest.mock import MagicMock, patch
from django.test import RequestFactory
from django.http import QueryDict

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings.test')
sys.path.insert(0, '/Users/<USER>/Projects/wheel-size-services')

django.setup()

from src.apps.widgets.api_proxy.views import FinderV2WidgetProxyView


def test_region_parameter_merging():
    """Test that region parameters from URL and widget config are properly merged."""
    
    # Create a mock request with region[] parameters
    factory = RequestFactory()
    request = factory.get('/api/mk?brands_exclude=aixam,acura&region[]=ladm&region[]=eudm&region[]=mxndm&region[]=cdm&region[]=usdm')
    
    # Create a mock widget configuration
    mock_config = MagicMock()
    mock_config.is_default = False
    mock_config.widget_type.allow_api = True
    
    # Mock params object with get_filter_params method
    mock_params = MagicMock()
    mock_params.get_filter_params.return_value = {
        'region': ['config_region1', 'config_region2'],
        'brands_exclude': 'aixam,acura'
    }
    mock_config.params = mock_params
    
    # Attach config to request
    request.config = mock_config
    
    # Create the view instance
    view = FinderV2WidgetProxyView()
    
    # Mock the get_chineze_params method
    view.get_chineze_params = MagicMock(return_value={})
    
    # Test the get_request_params method
    result_params = view.get_request_params(request)
    
    print("Test Results:")
    print(f"Input URL: {request.get_full_path()}")
    print(f"Query params: {dict(request.query_params.items())}")
    print(f"Region[] list: {request.query_params.getlist('region[]')}")
    print(f"Widget config regions: {mock_params.get_filter_params.return_value['region']}")
    print(f"Final merged params: {result_params}")
    
    # Verify that regions are properly merged
    if 'region' in result_params:
        merged_regions = result_params['region']
        print(f"Merged regions: {merged_regions}")
        
        # Check that widget config regions come first
        expected_start = ['config_region1', 'config_region2']
        assert merged_regions[:2] == expected_start, f"Widget config regions should come first: {merged_regions[:2]} != {expected_start}"
        
        # Check that URL regions are appended
        url_regions = ['ladm', 'eudm', 'mxndm', 'cdm', 'usdm']
        for region in url_regions:
            assert region in merged_regions, f"URL region '{region}' should be in merged regions: {merged_regions}"
        
        print("✅ Region merging test PASSED")
        return True
    else:
        print("❌ No 'region' parameter found in result")
        return False


def test_no_duplicate_regions():
    """Test that duplicate regions are not added when merging."""
    
    factory = RequestFactory()
    request = factory.get('/api/mk?region[]=usdm&region[]=cdm&region[]=new_region')
    
    mock_config = MagicMock()
    mock_config.is_default = False
    mock_config.widget_type.allow_api = True
    
    mock_params = MagicMock()
    mock_params.get_filter_params.return_value = {
        'region': ['usdm', 'cdm', 'widget_only_region'],  # usdm and cdm overlap with URL
    }
    mock_config.params = mock_params
    request.config = mock_config
    
    view = FinderV2WidgetProxyView()
    view.get_chineze_params = MagicMock(return_value={})
    
    result_params = view.get_request_params(request)
    
    print("\nDuplicate Test Results:")
    print(f"Widget config regions: ['usdm', 'cdm', 'widget_only_region']")
    print(f"URL regions: ['usdm', 'cdm', 'new_region']")
    print(f"Final merged params: {result_params}")
    
    if 'region' in result_params:
        merged_regions = result_params['region']
        print(f"Merged regions: {merged_regions}")
        
        # Check no duplicates
        assert len(merged_regions) == len(set(merged_regions)), f"Duplicate regions found: {merged_regions}"
        
        # Check expected regions are present
        expected_regions = ['usdm', 'cdm', 'widget_only_region', 'new_region']
        for region in expected_regions:
            assert region in merged_regions, f"Expected region '{region}' not found: {merged_regions}"
        
        print("✅ No duplicate regions test PASSED")
        return True
    else:
        print("❌ No 'region' parameter found in result")
        return False


if __name__ == '__main__':
    print("Testing region parameter merging in FinderV2WidgetProxyView...")
    print("=" * 70)
    
    try:
        test1_result = test_region_parameter_merging()
        test2_result = test_no_duplicate_regions()
        
        if test1_result and test2_result:
            print("\n🎉 All tests PASSED! Region parameter merging is working correctly.")
        else:
            print("\n💥 Some tests FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)