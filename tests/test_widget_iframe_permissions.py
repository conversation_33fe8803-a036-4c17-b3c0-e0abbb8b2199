"""
Test widget iframe permission checking after Django 4.2 upgrade.

This test verifies that the widget iframe preview works correctly with
port-aware hostname comparison in development environments.
"""

import pytest
from django.test import RequestFactory
from django.conf import settings
from unittest.mock import patch, MagicMock
import unittest

from src.apps.widgets.main.views.iframe import WidgetView


class WidgetIframePermissionTest(unittest.TestCase):
    """Test widget iframe permission checking with port-aware hostname comparison."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.view = WidgetView()

        # Mock widget configuration
        self.mock_config = MagicMock()
        self.mock_config.subscription = MagicMock()
        self.mock_config.subscription.deny_iframe.return_value = False
        self.mock_config.subscription.is_demo = False
        self.mock_config.subscription.is_trial = False
        self.mock_config.subscription.is_banned = False
        self.mock_config.params = {
            'permissions': {
                'domains': ['*.luxoft.com']
            }
        }

    def test_same_origin_request_with_port_ignoring_enabled(self):
        """Test same-origin check with port ignoring enabled (development mode)."""
        # Simulate development environment with port ignoring
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': True,
            'debug_csrf_validation': False
        }):
            request = self.factory.get('/widget/test/?config')
            request.META['HTTP_REFERER'] = 'http://development.local/widget/test/config/'
            request.META['HTTP_HOST'] = 'development.local:8000'

            self.view.request = request

            # Should return True for same-origin request with port ignoring
            result = self.view._is_same_origin_request('development.local')
            self.assertTrue(result)

    def test_same_origin_request_with_port_ignoring_disabled(self):
        """Test same-origin check with port ignoring disabled (production mode)."""
        # Simulate production environment with strict hostname checking
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': False,
            'debug_csrf_validation': False
        }):
            request = self.factory.get('/widget/test/?config')
            request.META['HTTP_REFERER'] = 'http://development.local/widget/test/config/'
            request.META['HTTP_HOST'] = 'development.local:8000'

            self.view.request = request

            # Should return False for hostname mismatch when port ignoring is disabled
            result = self.view._is_same_origin_request('development.local')
            self.assertFalse(result)

    def test_same_origin_request_exact_match(self):
        """Test same-origin check with exact hostname match."""
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': False,
            'debug_csrf_validation': False
        }):
            request = self.factory.get('/widget/test/?config')
            request.META['HTTP_REFERER'] = 'http://development.local:8000/widget/test/config/'
            request.META['HTTP_HOST'] = 'development.local:8000'

            self.view.request = request

            # Should return True for exact hostname match
            result = self.view._is_same_origin_request('development.local:8000')
            self.assertTrue(result)

    def test_check_permissions_same_origin_success(self):
        """Test check_permissions allows same-origin requests."""
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': True,
            'debug_csrf_validation': False
        }):
            request = self.factory.get('/widget/test/?config')
            request.META['HTTP_REFERER'] = 'http://development.local/widget/test/config/'
            request.META['HTTP_HOST'] = 'development.local:8000'

            self.view.request = request

            # Should not raise any exception for same-origin request
            try:
                result = self.view.check_permissions(self.mock_config)
                self.assertTrue(result)
            except Exception as e:
                self.fail(f"check_permissions raised an exception: {e}")

    def test_check_permissions_cross_origin_with_allowed_domain(self):
        """Test check_permissions allows cross-origin requests from allowed domains."""
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': True,
            'debug_csrf_validation': False
        }):
            # Mock WsLiveSettings to avoid database access
            with patch('ws_live_settings.models.WsLiveSettings.get', return_value=[]):
                request = self.factory.get('/widget/test/?config')
                request.META['HTTP_REFERER'] = 'http://client.luxoft.com/page-with-widget/'
                request.META['HTTP_HOST'] = 'development.local:8000'

                self.view.request = request
                self.view.config = self.mock_config

                # Should not raise any exception for allowed domain
                try:
                    result = self.view.check_permissions(self.mock_config)
                    self.assertTrue(result)
                except Exception as e:
                    self.fail(f"check_permissions raised an exception: {e}")

    def test_check_permissions_cross_origin_unauthorized_domain(self):
        """Test check_permissions blocks cross-origin requests from unauthorized domains."""
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': True,
            'debug_csrf_validation': False
        }):
            # Mock WsLiveSettings to avoid database access
            with patch('ws_live_settings.models.WsLiveSettings.get', return_value=[]):
                request = self.factory.get('/widget/test/?config')
                request.META['HTTP_REFERER'] = 'http://evil.com/page-with-stolen-widget/'
                request.META['HTTP_HOST'] = 'development.local:8000'

                self.view.request = request
                self.view.config = self.mock_config

                # Should raise Widget403 for unauthorized domain
                from src.apps.widgets.common.exceptions import Widget403
                with self.assertRaises(Widget403) as context:
                    self.view.check_permissions(self.mock_config)

                self.assertIn('not authorized for widget embedding', context.exception.message)

    def test_check_permissions_production_strict_hostname_checking(self):
        """Test that production environment enforces strict hostname checking."""
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': False,  # Production mode
            'debug_csrf_validation': False
        }):
            # Mock WsLiveSettings to avoid database access
            with patch('ws_live_settings.models.WsLiveSettings.get', return_value=[]):
                request = self.factory.get('/widget/test/?config')
                request.META['HTTP_REFERER'] = 'http://services.wheel-size.com/widget/test/config/'
                request.META['HTTP_HOST'] = 'services.wheel-size.com:443'  # Different port

                self.view.request = request
                self.view.config = self.mock_config

                # Should fail same-origin check and fall through to domain validation
                # Since mock_config has domains=['*.luxoft.com'], this should fail
                from src.apps.widgets.common.exceptions import Widget403
                with self.assertRaises(Widget403) as context:
                    self.view.check_permissions(self.mock_config)

                self.assertIn('not authorized for widget embedding', context.exception.message)

    def test_widget_embedding_security_scenarios(self):
        """Test comprehensive widget embedding security scenarios."""
        test_cases = [
            {
                'name': 'Authorized domain with wildcard',
                'referer': 'http://client.luxoft.com/page/',
                'host': 'development.local:8000',
                'domains': ['*.luxoft.com'],
                'should_pass': True
            },
            {
                'name': 'Authorized exact domain',
                'referer': 'http://luxoft.com/page/',
                'host': 'development.local:8000',
                'domains': ['luxoft.com'],
                'should_pass': True
            },
            {
                'name': 'Unauthorized domain',
                'referer': 'http://evil.com/stolen-widget/',
                'host': 'development.local:8000',
                'domains': ['*.luxoft.com'],
                'should_pass': False
            },
            {
                'name': 'Subdomain not matching wildcard',
                'referer': 'http://fake-luxoft.com/page/',
                'host': 'development.local:8000',
                'domains': ['*.luxoft.com'],
                'should_pass': False
            }
        ]

        for case in test_cases:
            with self.subTest(case=case['name']):
                with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
                    'ignore_port_in_hostname_check': True,
                    'debug_csrf_validation': False
                }):
                    # Mock WsLiveSettings and configure domains
                    with patch('ws_live_settings.models.WsLiveSettings.get', return_value=[]):
                        mock_config = MagicMock()
                        mock_config.subscription = MagicMock()
                        mock_config.subscription.deny_iframe.return_value = False
                        mock_config.subscription.is_demo = False
                        mock_config.subscription.is_trial = False
                        mock_config.subscription.is_banned = False
                        mock_config.params = {
                            'permissions': {
                                'domains': case['domains']
                            }
                        }

                        request = self.factory.get('/widget/test/')
                        request.META['HTTP_REFERER'] = case['referer']
                        request.META['HTTP_HOST'] = case['host']

                        self.view.request = request
                        self.view.config = mock_config

                        if case['should_pass']:
                            try:
                                result = self.view.check_permissions(mock_config)
                                self.assertTrue(result)
                            except Exception as e:
                                self.fail(f"Expected {case['name']} to pass, but got: {e}")
                        else:
                            from src.apps.widgets.common.exceptions import Widget403
                            with self.assertRaises(Widget403):
                                self.view.check_permissions(mock_config)

    def test_debug_logging_enabled(self):
        """Test debug logging when enabled."""
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': True,
            'debug_csrf_validation': True
        }):
            request = self.factory.get('/widget/test/?config')
            request.META['HTTP_HOST'] = 'development.local:8000'

            self.view.request = request

            with patch('builtins.print') as mock_print:
                self.view._debug_log("Test message")
                mock_print.assert_called_once_with("🔍 WIDGET IFRAME DEBUG: Test message")

    def test_debug_logging_disabled(self):
        """Test debug logging when disabled."""
        with patch.object(settings, 'WIDGET_CSRF_SETTINGS', {
            'ignore_port_in_hostname_check': True,
            'debug_csrf_validation': False
        }):
            request = self.factory.get('/widget/test/?config')
            request.META['HTTP_HOST'] = 'development.local:8000'

            self.view.request = request

            with patch('builtins.print') as mock_print:
                self.view._debug_log("Test message")
                mock_print.assert_not_called()


if __name__ == '__main__':
    pytest.main([__file__])
