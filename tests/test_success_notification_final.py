#!/usr/bin/env python3
"""
Final test to verify that the success notification system works correctly
for the finder-v2 widget configuration page.

This test verifies:
1. The template has the correct message display structure
2. The views have the success message code
3. The Django messages framework is properly configured
"""

import requests
import re
from bs4 import BeautifulSoup

def test_template_structure():
    """Test that the template has the correct message display structure."""
    
    print("🧪 Testing Template Structure")
    print("=" * 50)
    
    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"
    
    response = requests.get(config_url)
    
    if response.status_code != 200:
        print(f"❌ Failed to load page. Status: {response.status_code}")
        return False
    
    print(f"✅ Page loaded successfully (Status: {response.status_code})")
    
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Check if the messages section exists in the template
    messages_comment = soup.find(string=re.compile(r'Success/Error Messages'))
    
    if messages_comment:
        print("✅ Messages section comment found in template")
    else:
        print("❌ Messages section comment not found in template")
        return False
    
    # Check if the template has the Django messages loop structure
    template_content = response.text

    if '{% if messages %}' in template_content or 'if messages' in template_content:
        print("✅ Django messages loop found in template")
    else:
        print("❌ Django messages loop not found in template")
        print("Template content sample:")
        # Show a sample of the template around the messages section
        lines = template_content.split('\n')
        for i, line in enumerate(lines):
            if 'Success/Error Messages' in line:
                start = max(0, i-2)
                end = min(len(lines), i+10)
                for j in range(start, end):
                    print(f"  {j+1}: {lines[j]}")
                break
        return False
    
    # Check if the success message div structure exists (even if empty)
    # Look for the TailwindCSS classes we added
    template_content = response.text
    
    if 'rounded-md bg-green-50 p-4 mb-6' in template_content:
        print("✅ Success message div structure found in template")
    else:
        print("❌ Success message div structure not found in template")
        return False
    
    if 'Configuration widget has been saved successfully' in template_content:
        print("✅ Success message text found in template")
    else:
        print("❌ Success message text not found in template")
        return False
    
    return True

def test_view_implementation():
    """Test that the views have the success message implementation."""
    
    print("\n🧪 Testing View Implementation")
    print("=" * 50)
    
    # Read the view file to check if success messages are implemented
    try:
        with open('/Users/<USER>/Projects/wheel-size-services/src/apps/widgets/main/views/config.py', 'r') as f:
            view_content = f.read()
        
        # Check if Django messages import exists
        if 'from django.contrib import messages' in view_content:
            print("✅ Django messages import found in views")
        else:
            print("❌ Django messages import not found in views")
            return False
        
        # Check if success message is added in form_valid methods
        success_message_count = view_content.count("messages.success(self.request, _('Configuration widget has been saved successfully'))")
        
        if success_message_count >= 2:
            print(f"✅ Success message implementation found in {success_message_count} places")
        else:
            print(f"❌ Success message implementation found in only {success_message_count} places (expected 2)")
            return False
        
        # Check if form_valid methods exist
        if 'def form_valid(self, form):' in view_content:
            print("✅ form_valid methods found in views")
        else:
            print("❌ form_valid methods not found in views")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading view file: {e}")
        return False

def test_django_configuration():
    """Test that Django messages framework is properly configured."""
    
    print("\n🧪 Testing Django Configuration")
    print("=" * 50)
    
    # Test if the messages framework is working by checking session support
    session = requests.Session()
    
    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"
    
    # Make a request to see if we get session cookies
    response = session.get(config_url)
    
    if 'sessionid' in session.cookies or 'csrftoken' in session.cookies:
        print("✅ Django session/CSRF cookies found - messages framework should work")
    else:
        print("❌ No Django session cookies found - messages framework may not work")
        return False
    
    # Check if the response includes Django-specific headers
    if 'text/html' in response.headers.get('Content-Type', ''):
        print("✅ HTML response received - template rendering works")
    else:
        print("❌ Non-HTML response received")
        return False
    
    return True

def test_form_structure():
    """Test that the form has the required structure for submission."""
    
    print("\n🧪 Testing Form Structure")
    print("=" * 50)
    
    base_url = "http://development.local:8000"
    config_url = f"{base_url}/widget/fd77b77985314fbab6047e9fa420360d/config-demo/"
    
    response = requests.get(config_url)
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Check if the configuration form exists
    form = soup.find('form', class_='config-form')
    
    if not form:
        print("❌ Configuration form not found")
        return False
    
    print("✅ Configuration form found")
    
    # Check if CSRF token exists
    csrf_token = form.find('input', {'name': 'csrfmiddlewaretoken'})
    
    if not csrf_token:
        print("❌ CSRF token not found")
        return False
    
    print("✅ CSRF token found")
    
    # Check if form has the correct action URL
    action = form.get('action', '')
    
    if 'config-demo' in action:
        print("✅ Form action URL is correct")
    else:
        print(f"❌ Form action URL is incorrect: {action}")
        return False
    
    # Count form fields
    input_fields = form.find_all(['input', 'select', 'textarea'])
    print(f"✅ Form has {len(input_fields)} fields")
    
    return True

def main():
    """Run all tests to verify the success notification system."""
    
    print("Testing Success Notification System for Finder-v2 Widget")
    print("=" * 60)
    
    tests = [
        ("Template Structure", test_template_structure),
        ("View Implementation", test_view_implementation),
        ("Django Configuration", test_django_configuration),
        ("Form Structure", test_form_structure),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed with error: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\nThe success notification system is properly implemented.")
        print("When a user successfully submits the widget configuration form,")
        print("they will see a green success notification at the top of the page.")
        print("\nTo test manually:")
        print("1. Go to: http://development.local:8000/widget/fd77b77985314fbab6047e9fa420360d/config-demo/")
        print("2. Fill out the form with valid data")
        print("3. Click 'Save Configuration'")
        print("4. You should see the success notification after the page reloads")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the implementation and fix any issues.")

if __name__ == "__main__":
    main()
