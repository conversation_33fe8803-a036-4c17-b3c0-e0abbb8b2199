#!/usr/bin/env python3
"""
Simple test to verify the region parameter merging logic.
This tests the core logic without requiring full Django setup.
"""

def test_region_merging_logic():
    """Test the core logic of region parameter merging."""
    
    # Simulate widget config regions
    widget_regions = ['config_region1', 'config_region2']
    
    # Simulate URL regions (from region[] parameters)
    url_regions = ['ladm', 'eudm', 'mxndm', 'cdm', 'usdm']
    
    # Apply the merging logic from our fix
    merged_regions = list(widget_regions)  # Start with widget config regions
    for region in url_regions:
        if region not in merged_regions:
            merged_regions.append(region)
    
    print("Region Merging Logic Test:")
    print(f"Widget config regions: {widget_regions}")
    print(f"URL regions: {url_regions}")
    print(f"Merged regions: {merged_regions}")
    
    # Verify widget config regions come first
    assert merged_regions[:2] == widget_regions, f"Widget regions should come first"
    
    # Verify all URL regions are included
    for region in url_regions:
        assert region in merged_regions, f"URL region '{region}' should be included"
    
    # Verify no duplicates
    assert len(merged_regions) == len(set(merged_regions)), "No duplicates should exist"
    
    print("✅ Region merging logic test PASSED")
    return True


def test_duplicate_handling():
    """Test handling of duplicate regions between widget config and URL."""
    
    # Widget config has some overlapping regions
    widget_regions = ['usdm', 'cdm', 'widget_only']
    
    # URL has some overlapping and some new regions
    url_regions = ['usdm', 'cdm', 'eudm', 'url_only']
    
    # Apply the merging logic
    merged_regions = list(widget_regions)
    for region in url_regions:
        if region not in merged_regions:
            merged_regions.append(region)
    
    print("\nDuplicate Handling Test:")
    print(f"Widget config regions: {widget_regions}")
    print(f"URL regions: {url_regions}")
    print(f"Merged regions: {merged_regions}")
    
    # Expected result: ['usdm', 'cdm', 'widget_only', 'eudm', 'url_only']
    expected = ['usdm', 'cdm', 'widget_only', 'eudm', 'url_only']
    
    assert merged_regions == expected, f"Expected {expected}, got {merged_regions}"
    assert len(merged_regions) == len(set(merged_regions)), "No duplicates should exist"
    
    print("✅ Duplicate handling test PASSED")
    return True


def test_user_scenario():
    """Test the specific user scenario from the issue."""
    
    # Widget configuration regions (example)
    widget_regions = ['widget_region1', 'widget_region2']
    
    # URL regions from: region[]=ladm&region[]=eudm&region[]=mxndm&region[]=cdm&region[]=usdm
    url_regions = ['ladm', 'eudm', 'mxndm', 'cdm', 'usdm']
    
    # Apply merging logic
    merged_regions = list(widget_regions)
    for region in url_regions:
        if region not in merged_regions:
            merged_regions.append(region)
    
    print("\nUser Scenario Test:")
    print(f"Input URL would have: region[]=ladm&region[]=eudm&region[]=mxndm&region[]=cdm&region[]=usdm")
    print(f"Widget config regions: {widget_regions}")
    print(f"URL regions: {url_regions}")
    print(f"Final merged regions: {merged_regions}")
    
    # The result should contain all regions without duplicates
    # Widget regions first, then URL regions
    expected_regions = widget_regions + url_regions
    assert all(r in merged_regions for r in expected_regions), "All regions should be present"
    
    # When passed to requests library, this would become:
    # region=widget_region1&region=widget_region2&region=ladm&region=eudm&region=mxndm&region=cdm&region=usdm
    print(f"Final API call would have: {'&'.join(f'region={r}' for r in merged_regions)}")
    
    print("✅ User scenario test PASSED")
    return True


if __name__ == '__main__':
    print("Testing region parameter merging logic...")
    print("=" * 60)
    
    try:
        test1 = test_region_merging_logic()
        test2 = test_duplicate_handling()
        test3 = test_user_scenario()
        
        if test1 and test2 and test3:
            print("\n🎉 All tests PASSED! Region parameter merging logic is correct.")
        else:
            print("\n💥 Some tests FAILED!")
            exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        exit(1)