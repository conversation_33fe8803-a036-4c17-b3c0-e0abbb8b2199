{"permissions": {"allow": ["Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(python3:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "Bash(./deploy-finder-v2.sh:*)", "Bash(find:*)", "Bash(rg:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(poetry run python:*)", "Bash(grep:*)", "Bash(pytest:*)", "<PERSON><PERSON>(poetry run pytest:*)", "Bash(DJANGO_SETTINGS_MODULE=src.settings.dev_docker python debug_region_test.py)"], "deny": []}}