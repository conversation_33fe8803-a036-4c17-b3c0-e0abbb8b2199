# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Python/Django Environment
```bash
# Install dependencies (uses Poetry)
poetry install

# Run development server
poetry run python manage.py runserver

# Run Django migrations
poetry run python manage.py migrate

# Create new migrations
poetry run python manage.py makemigrations

# Run Django shell
poetry run python manage.py shell

# Collect static files
poetry run python manage.py collectstatic --noinput

# Load/sync data fixtures
poetry run python manage.py ws_load

# Django management commands
poetry run python manage.py check
poetry run python manage.py test
```

### Frontend Development (Widget Apps)
```bash
# Build Finder-v2 widget (Vue 3 + TailwindCSS v4)
cd src/apps/widgets/finder_v2/app
npm install
npm run build  # Production build
npm run dev    # Development with hot reload

# Test Finder-v2 widget
npm run test          # Run tests
npm run test:coverage # Run with coverage
npm run lint          # ESLint check
```

### Testing
```bash
# Run comprehensive widget security tests
python tests/widget/test_widget_csrf_security.py

# Run specific widget tests
python -m pytest tests/widget/

# Test widget integration
python tests/test_widget_iframe_permissions.py
```

### Docker Development
```bash
# Start development environment
docker-compose up -d

# View application logs
docker logs -f ws_services

# Access database
docker exec -it postgres15 psql -U ws_services_user -d ws_services_db
```

## Architecture Overview

### Widget System Architecture
This is a Django-based SaaS platform providing embeddable wheel/tire finder widgets for client websites. The system uses a multi-tenant architecture where each client gets customizable widgets with unique configurations.

**Core Components:**
- **Widget Types**: Base classes defining widget behavior (`src/apps/widgets/widget_type.py`)
- **API Proxy**: Secure API gateway with CSRF protection (`src/apps/widgets/api_proxy/`)
- **Widget Apps**: Individual widget implementations (finder, finder_v2, calc)
- **Admin Interface**: Django admin for widget configuration
- **Translation System**: Multi-language support for widgets

### Widget Request Flow
1. Client website loads widget JavaScript from services.wheel-size.com
2. Widget JavaScript generates CSRF token based on User-Agent
3. Widget makes API calls to `/widget/{type}/api/` endpoints with CSRF token
4. Server validates token via `WsProtectMixin` and proxies to backend API
5. Response returned to widget with proper CORS headers

### CSRF Protection Model
The widget system uses custom CSRF protection designed for cross-domain operation:
- **Token Generation**: Base64-encoded User-Agent with algorithmic transformation
- **Validation**: Server validates token on every API request regardless of origin
- **Security**: No hostname bypass vulnerabilities, strict token validation

### Key File Locations
- **Settings**: `src/settings/` (environment-specific configurations)
- **Widget Types**: `src/apps/widgets/{widget_name}/widget_type.py`
- **API Proxy**: `src/apps/widgets/api_proxy/views.py` (CSRF protection)
- **Templates**: `src/templates/widgets/{widget_name}/`
- **Static Files**: Widget build outputs in `src/apps/widgets/{widget_name}/static/`

### Database Configuration
- **Development**: Uses staging database by default (`DATABASES['default']` = `DATABASES['stg']`)
- **Multiple DBs**: `default`, `stg`, `prod` connections available
- **PostgreSQL 15**: Primary database engine

### Widget Development Patterns
- **Widget Type Registration**: All widget types auto-registered via imports in `widget_type.py`
- **Configuration**: Default configs in `default_config/config.py`
- **Themes**: Visual themes in `default_config/themes.py`
- **Forms**: Admin forms in `forms.py`
- **Translations**: JSON translation files in `translations/`

### Frontend Architecture (Finder-v2)
- **Vue 3**: Modern reactive frontend framework
- **TailwindCSS v4**: Utility-first CSS framework
- **Vite**: Build tool and development server
- **Vitest**: Testing framework

### Security Considerations
- **CSRF Token**: Required for all widget API requests
- **Cross-Origin**: Widgets work on client domains but maintain security
- **Widget CSRF Settings**: Configured per environment in Django settings
- **API Throttling**: Rate limiting on widget API endpoints

### Environment Variables
Key environment variables for development:
- `DJANGO_SETTINGS_MODULE`: Set to appropriate settings module
- `DEBUG`: Enable debug mode
- `WIDGET_CSRF_IGNORE_PORT`: Ignore port in hostname checks (dev only)
- `WIDGET_CSRF_TRUSTED_HOSTS`: Trusted hostnames (use carefully)
- `WIDGET_CSRF_DEBUG`: Enable CSRF debug logging

### Common Development Workflows
1. **Adding New Widget Type**: Create widget app, inherit from `WidgetType`, register in imports
2. **Widget Configuration**: Modify default_config, forms, and themes
3. **Frontend Changes**: Edit source files, run build commands, test in admin interface
4. **API Changes**: Update proxy views, test CSRF protection, validate security
5. **Security Testing**: Run widget security tests after any API/CSRF changes