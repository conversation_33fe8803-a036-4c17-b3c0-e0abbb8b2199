# Region Filtering Issue - Diagnostic Report

## Problem Summary
Chinese brands like "Aion" are appearing in finder-v2 widget results even when region filtering should exclude Chinese regions (chdm).

## Technical Investigation Results

### 1. Parameter Processing Logic ✅ WORKING CORRECTLY
Our test confirms that the `FinderV2WidgetProxyView.get_request_params()` method correctly:
- Processes `region[]` URL parameters 
- Prioritizes URL regions over widget configuration
- Excludes Chinese region `chdm` when user provides `['usdm', 'cdm']`
- Sends correct parameters: `region=['usdm', 'cdm']`

### 2. Root Cause Analysis
Since parameter processing is working correctly, the issue must be elsewhere:

#### Potential Causes:
1. **Upstream API Issue**: The wheel-size.com API itself may not be properly filtering by region
2. **Cache Issue**: Results may be cached and not reflecting the region filter
3. **API Request Format**: The way multiple region parameters are serialized in the HTTP request
4. **Brand Database Issue**: Chinese brands might be incorrectly categorized in the upstream database

### 3. Expected vs Actual Behavior

**Expected API Request:**
```
GET /v2/makes/?region=usdm&region=cdm
```

**Expected Result:** Only US Domestic and Canadian brands
**Actual Result:** Chinese brands like "Aion" still appear

## Recommended Next Steps

### 1. Verify Actual HTTP Request 
Add logging to capture the exact HTTP request being sent to wheel-size.com:

```python
# In FinderV2WidgetProxyView, add to get_proxy_request_headers():
import logging
logger = logging.getLogger(__name__)
logger.error(f"REGION DEBUG - Final request URL: {self.get_proxy_url(request)}")
logger.error(f"REGION DEBUG - Final params: {self.get_request_params(request)}")
```

### 2. Test Direct API Call
Test the upstream API directly to verify region filtering:
```bash
curl "https://api3.wheel-size.com/v2/makes/?region=usdm&region=cdm" \
  -H "X-WS-API-SECRET-TOKEN: [token]"
```

### 3. Check Widget Configuration
Verify that the widget configuration doesn't include `chdm` regions:
```python
# Check actual widget config in database
request.config.params.get_filter_params()
```

### 4. Check for Multiple API Calls
The widget might be making multiple API calls with different parameters. Check browser network tab for all `/makes/` requests.

## Current Status
- ✅ Parameter processing logic is correct
- ✅ Chinese regions are properly excluded from request
- ❌ Chinese brands still appear in results
- ❓ Need to verify actual HTTP request being sent to upstream API

## Files Modified
- `src/apps/widgets/api_proxy/views.py` - Fixed parameter processing in lines 375-444
- Region filtering prioritizes URL parameters over widget configuration
- Proper handling of `region[]` array parameters

## Test Files
- `test_region_debug.py` - Confirms parameter processing works correctly
- `tests/widget/finder_v2/test_region_filtering.py` - Unit tests for region normalization

The issue appears to be downstream from our parameter processing - either in the HTTP request serialization, upstream API behavior, or caching.