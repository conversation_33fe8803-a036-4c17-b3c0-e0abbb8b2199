# Development Preferences
- User prefers using Docker for containerization and Poetry for Python project management and dependency handling.
- User prefers using development.local host instead of localhost for local development environments.
- User prefers creating proper forks and publishing to private PyPI instead of patching site-packages directly, following the WS package upgrade pattern (v1.x → v2.0.0) for maintainability.
- User prefers using feature branches named 'feature/django-4.2-upgrade' for WS package upgrades, pushing to origin without merging to main branches.
- User prefers modifying WS packages directly when needed for bug fixes rather than implementing workarounds.
- User prefers using published versions from private PyPI instead of local development versions for WS packages.
- User prefers ws-django-tire-calc version 2.0.0 instead of 2.0.2 for the wheel-size-services project.
- User prefers step-by-step execution of upgrade phases with progress marked in documentation files for visibility.
- User prefers continuous execution of tasks without asking for permission to continue - work until issues are completely solved.

# Documentation Standards
- User requires all Markdown documentation files to include 'Last Modified: YYYY-MM-DD HH:MM UTC+6' timestamp immediately after the H1 title.
- User prefers concise documentation with only key information, detailed GRUNT build process documentation, and comprehensive Python code comments.
- User prefers organized project structure with tests/ and docs/ directories, consistent file naming conventions, comprehensive README.md as documentation hub.
- User prefers moving detailed comments from security-sensitive JavaScript files to separate markdown documentation files in the same folder for security reasons.
- User prefers documenting API testing procedures including CSRF token testing commands in markdown files for widget endpoints.
- User prefers constantly marking progress in markdown files during task execution for visibility and tracking.
- User prefers updating PRODUCTION_DEPLOYMENT_GUIDE.md whenever Django migrations need to be applied.
- For small issues and bug fixes like generation display logic, consolidate documentation into existing knowledge transfer files rather than creating multiple separate markdown files to avoid documentation sprawl and maintain organized project documentation.
- Always update the main finder-v2 knowledge transfer document at `/Users/<USER>/Projects/wheel-size-services/docs/development/finder-v2-knowledge-transfer.md` whenever major updates, bug fixes, or feature implementations are made to the finder-v2 widget, including major code changes, bug fixes, new features, API changes, build/deployment updates, and critical fixes - consolidate information rather than creating separate documentation files.

# Implementation Approach
- User prefers structured phase-by-phase implementation with progress tracking via checkbox updates in markdown files, regular commits with descriptive messages.
- User prefers systematic implementation plans with feature specifications, sequential phases, progress tracking checkboxes, technical requirements, dependencies, testing strategies.
- User requires 100% backward compatibility for existing client widget installations during Django upgrades.
- User prefers systematic chain-of-thought reasoning for debugging widget API routing issues, including checking for cached JavaScript files and browser developer tools.
- After making code fixes during development, always restart Django/Docker containers to ensure changes are properly loaded and applied.
- User prefers structured 5-phase implementation approach with checkbox progress tracking in markdown files, sequential phase completion with testing after major changes, and maintaining unified template systems during UI modernization projects.
- User prefers sequential phase-by-phase implementation with progress tracking via markdown checkboxes, maintaining 100% backward compatibility, and comprehensive documentation updates in knowledge-transfer files for finder-v2 widget development.

# Django & Package Upgrades
- User prefers following current Django best practices for upgrades, including security improvements and modernized settings configurations.
- For Django package upgrades: use major version increments (v1.x → v2.0.0), support Django 3.2-4.2 LTS and Python 3.9-3.12, remove six dependency, modernize code.
- User prefers configurable CSRF hostname validation that works across development (with ports) and production environments.
- For publishing WS packages to private PyPI: use 'poetry config repositories.ws https://pypi.wheel-size.com/', 'poetry config http-basic.ws repo-user Ojc3ZSPwIBEEisX', and 'poetry publish -r ws --build' commands.

# Widget Development
- User prefers implementing public widget creation interfaces for new widget types (like finder-v2) that follow the same patterns as existing public creation functionality.
- User prefers simplified widget configuration interfaces with 'Choose Primary Tab' and 'Choose Widget Tabs' functionality removed from finder-v2 widget configuration forms.
- User prefers widget configuration URLs in format /widget/{slug}/config/ rather than /widget/{widget-type}/{slug}/config/ for the wheel-size-services project.
- Widget portal security model: same-origin iframe previews should always work in widget configuration pages, cross-domain embedding is controlled by authorized domains list.
- For finder-v2 widget development, prioritize make/model/year API endpoints over tire/rim search endpoints since v2 API has different structures than v1.
- Finder-v2 widget was implemented with only VehicleSearch component (Year→Make→Model and Make→Model→Generation→Modifications flows).
- User prefers using ./deploy-finder-v2.sh script as the automated deployment script for finder-v2 Vue.js development workflow.
- User prefers modernizing finder-v2 widget brand filtering by replacing Bootstrap tabs with TailwindUI components, implementing 3-tab structure, removing regions functionality.
- User prefers regions functionality in finder-v2 widget to be implemented as a separate section below the brand filtering tabs rather than as a 4th tab in the tab structure.
- Generation objects have unique 'start' and 'end' integer properties that model objects lack, making these the correct discriminator properties to distinguish generations from models in CustomSelector formatting logic.

# UI/Frontend Development
- User prefers not to use RequireJS.
- User is considering adopting TailwindCSS v4.1 for wheel-size-services project frontend styling.
- User prefers using ws-secondary colors instead of gray colors in TailwindCSS templates.
- User prefers replacing existing navigation components with TailwindUI components while maintaining 100% backward compatibility.
- User prefers comprehensive TailwindCSS v4.1 documentation covering implementation details, build processes, theme configuration, content scanning.
- The wheel-size-services project has two separate TailwindCSS configurations: one for the Django portal management area (src/apps/portal/static/portal/css/tailwind.config.js) and one for the Vue.js widget iframe application (src/apps/widgets/finder_v2/app/tailwind.config.js), each serving different scopes and build processes.
- User prefers minimal TailwindCSS configurations that rely on content scanning from Vue components rather than explicit safelists to minimize bundle size for widget applications.
- User prefers TailwindCSS green notification pattern with specific HTML structure (rounded-md bg-green-50 p-4 with SVG icon) for success messages in widget configuration pages, using Django flash messages for post-save feedback.
- When verifying TailwindCSS classes, check modern utilities like size-*, divide-*, ring-*, responsive variants (sm:*), screen reader utilities (sr-only), and advanced flexbox utilities - if missing, either add to safelist or suggest alternatives with same visual result.