import { productionPatterns, brandColors, stateColors } from './tailwind.shared.js';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    // Development: Include ALL possible content to force generation
    "../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}",
    "../../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}",
    "../js/**/*.{js,ts}",
    "../../../**/*.{html,js,ts,jsx,tsx,vue}",
    
    // Force generation by including a wide pattern
    "./**/*.{html,js,ts,jsx,tsx,vue,php,twig,erb,blade}",
    
    // Safelist patterns to force ALL standard utilities
    { raw: 'space-y-0.5 space-y-1 space-y-2 space-y-3 space-y-4 space-y-6 space-y-8' },
    { raw: 'space-x-0.5 space-x-1 space-x-2 space-x-3 space-x-4 space-x-6 space-x-8' },
    { raw: 'p-0.5 p-1 p-2 p-3 p-4 p-5 p-6 p-7 p-8 p-9 p-10 p-12 p-16 p-20 p-24' },
    { raw: 'px-0.5 px-1 px-2 px-3 px-4 px-5 px-6 px-8 py-0.5 py-1 py-2 py-3 py-4 py-6 py-8' },
    { raw: 'text-xs text-sm text-base text-lg text-xl text-2xl text-3xl text-4xl' },
    { raw: 'bg-gray-50 bg-gray-100 bg-gray-200 bg-gray-300 bg-gray-400 bg-gray-500' },
    { raw: 'flex inline-flex grid inline-grid block inline-block hidden' },
    { raw: 'items-center items-start items-end justify-center justify-between' },

    // Finder-v2 widget specific utilities (for success notifications and layout)
    { raw: 'size-5 size-20 shrink-0 sr-only shadow-xs' },
    { raw: 'ring-1 ring-inset ring-gray-300' },
    { raw: 'divide-y divide-x divide-gray-200 sm:divide-x sm:divide-y-0' },
    { raw: 'sm:grid-cols-3 sm:items-center sm:justify-between sm:space-x-5 sm:pt-1 sm:mt-0 sm:text-left sm:text-2xl' },
    { raw: 'bg-green-50 text-green-400 text-green-700 text-green-800 border-green-200' },
    { raw: 'bg-red-50 text-red-400 text-red-700 text-red-800 border-red-200' },
    { raw: 'bg-yellow-50 text-yellow-400 text-yellow-700 text-yellow-800 border-yellow-200' },
    { raw: 'bg-blue-50 text-blue-400 text-blue-700 text-blue-800 border-blue-200' },
    { raw: 'mb-6 ml-3 mt-2 rounded-md font-medium' }
  ],



  // Add missing colors for TailwindCSS v4.1
  theme: {
    extend: {
      colors: {
        purple: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        red: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        blue: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        green: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        yellow: {
          50: '#fefce8',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
        },
        pink: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        }
      }
    }
  },

  // Full safelist for production convenience
  safelist: [
    // Use shared production patterns for consistency
    ...productionPatterns,
    
    // Explicit purple utility classes (TailwindCSS v4.1 pattern matching is unreliable)
    'border-purple-200', 'border-purple-300', 'border-purple-600', 'border-purple-700',
    'text-purple-600', 'text-purple-700', 'text-purple-800', 'text-purple-900',
    'bg-purple-50', 'bg-purple-100', 'bg-purple-200', 'bg-purple-300', 'bg-purple-400', 'bg-purple-500', 'bg-purple-600', 'bg-purple-700', 'bg-purple-800', 'bg-purple-900',
    'hover:border-transparent', 'hover:bg-purple-600', 'hover:text-white', 'active:bg-purple-700',
    
    // Transparent borders and hover states
    'border-transparent', 'hover:border-transparent', 'hover:text-white', 
    'active:bg-purple-700', 'hover:bg-purple-600',
    
    // Other color utilities that might be missing
    'border-red-200', 'border-red-300', 'border-red-600', 'border-red-700',
    'text-red-600', 'text-red-700', 'bg-red-50', 'bg-red-100', 'bg-red-200', 'bg-red-600', 'bg-red-700',
    'border-blue-200', 'border-blue-300', 'border-blue-600', 'border-blue-700',
    'text-blue-600', 'text-blue-700', 'bg-blue-50', 'bg-blue-100', 'bg-blue-200', 'bg-blue-600', 'bg-blue-700',
    'border-green-200', 'border-green-300', 'border-green-600', 'border-green-700',
    'text-green-600', 'text-green-700', 'bg-green-50', 'bg-green-100', 'bg-green-200', 'bg-green-600', 'bg-green-700',
    
    // Hover and active states
    'hover:border-transparent', 'hover:text-white',
    
    // Brand colors (from original config)
    'bg-ws-primary-50', 'bg-ws-primary-100', 'bg-ws-primary-200', 'bg-ws-primary-300', 'bg-ws-primary-400', 'bg-ws-primary-500', 'bg-ws-primary-600', 'bg-ws-primary-700', 'bg-ws-primary-800', 'bg-ws-primary-900',
    'text-ws-primary-50', 'text-ws-primary-100', 'text-ws-primary-200', 'text-ws-primary-300', 'text-ws-primary-400', 'text-ws-primary-500', 'text-ws-primary-600', 'text-ws-primary-700', 'text-ws-primary-800', 'text-ws-primary-900'
  ],

  // Enable dark mode
  darkMode: 'class'
} 