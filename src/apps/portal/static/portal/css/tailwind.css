@import "tailwindcss";

/* TailwindCSS v4.1 Theme Configuration */
@theme {
  /* Brand Colors - Wheel-Size.com theme */
  --color-ws-primary-50: #fef2f2;
  --color-ws-primary-100: #fee2e2;
  --color-ws-primary-200: #fecaca;
  --color-ws-primary-300: #fca5a5;
  --color-ws-primary-400: #f87171;
  --color-ws-primary-500: #ef4444;
  --color-ws-primary-600: #dc2626;
  --color-ws-primary-700: #be3e1d;  /* Main brand color */
  --color-ws-primary-800: #991b1b;
  --color-ws-primary-900: #7f1d1d;

  /* Secondary Colors */
  --color-ws-secondary-50: #f8fafc;
  --color-ws-secondary-100: #f1f5f9;
  --color-ws-secondary-200: #e2e8f0;
  --color-ws-secondary-300: #cbd5e1;
  --color-ws-secondary-400: #94a3b8;
  --color-ws-secondary-500: #64748b;
  --color-ws-secondary-600: #475569;
  --color-ws-secondary-700: #334155;
  --color-ws-secondary-800: #1e293b;
  --color-ws-secondary-900: #0f172a;

  /* State Colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-200: #fecaca;
  --color-danger-300: #fca5a5;
  --color-danger-400: #f87171;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  --color-danger-800: #991b1b;
  --color-danger-900: #7f1d1d;

  /* Typography */
  --font-family-sans: 'Open Sans', system-ui, -apple-system, sans-serif;
  --font-family-heading: 'Rambla', system-ui, -apple-system, sans-serif;
  --font-family-accent: 'Calligraffitti', cursive;
}

/* TailwindCSS v4.1 Custom Color Variables */
@layer theme {
  :root {
    /* Purple color palette - missing from TailwindCSS v4.1 default */
    --color-purple-50: #faf5ff;
    --color-purple-100: #f3e8ff;
    --color-purple-200: #e9d5ff;
    --color-purple-300: #d8b4fe;
    --color-purple-400: #c084fc;
    --color-purple-500: #a855f7;
    --color-purple-600: #9333ea;
    --color-purple-700: #7c3aed;
    --color-purple-800: #6b21a8;
    --color-purple-900: #581c87;

    /* Red color palette */
    --color-red-50: #fef2f2;
    --color-red-100: #fee2e2;
    --color-red-200: #fecaca;
    --color-red-300: #fca5a5;
    --color-red-400: #f87171;
    --color-red-500: #ef4444;
    --color-red-600: #dc2626;
    --color-red-700: #b91c1c;
    --color-red-800: #991b1b;
    --color-red-900: #7f1d1d;

    /* Extended blue color palette */
    --color-blue-50: #eff6ff;
    --color-blue-100: #dbeafe;
    --color-blue-200: #bfdbfe;
    --color-blue-300: #93c5fd;
    --color-blue-400: #60a5fa;
    /* --color-blue-500: already defined by TailwindCSS v4.1 */
    --color-blue-600: #2563eb;
    --color-blue-700: #1d4ed8;
    --color-blue-800: #1e40af;
    --color-blue-900: #1e3a8a;

    /* Green color palette */
    --color-green-50: #f0fdf4;
    --color-green-100: #dcfce7;
    --color-green-200: #bbf7d0;
    --color-green-300: #86efac;
    --color-green-400: #4ade80;
    --color-green-500: #22c55e;
    --color-green-600: #16a34a;
    --color-green-700: #15803d;
    --color-green-800: #166534;
    --color-green-900: #14532d;

    /* Yellow color palette */
    --color-yellow-50: #fefce8;
    --color-yellow-100: #fef3c7;
    --color-yellow-200: #fde68a;
    --color-yellow-300: #fcd34d;
    --color-yellow-400: #fbbf24;
    --color-yellow-500: #f59e0b;
    --color-yellow-600: #d97706;
    --color-yellow-700: #b45309;
    --color-yellow-800: #92400e;
    --color-yellow-900: #78350f;

    /* Indigo color palette */
    --color-indigo-50: #eef2ff;
    --color-indigo-100: #e0e7ff;
    --color-indigo-200: #c7d2fe;
    --color-indigo-300: #a5b4fc;
    --color-indigo-400: #818cf8;
    --color-indigo-500: #6366f1;
    --color-indigo-600: #4f46e5;
    --color-indigo-700: #4338ca;
    --color-indigo-800: #3730a3;
    --color-indigo-900: #312e81;

    /* Pink color palette */
    --color-pink-50: #fdf2f8;
    --color-pink-100: #fce7f3;
    --color-pink-200: #fbcfe8;
    --color-pink-300: #f9a8d4;
    --color-pink-400: #f472b6;
    --color-pink-500: #ec4899;
    --color-pink-600: #db2777;
    --color-pink-700: #be185d;
    --color-pink-800: #9d174d;
    --color-pink-900: #831843;
  }
}

/* Custom Component Classes */
@layer components {
  /* Container centering and padding - TailwindCSS v4.1 container doesn't include these by default */
  .container {
    margin-inline: auto !important;
    padding-inline: 1rem !important; /* 16px base padding */
  }

  @media (width >= 40rem) {
    .container {
      padding-inline: 1.5rem !important; /* 24px padding on small screens */
    }
  }

  @media (width >= 64rem) {
    .container {
      padding-inline: 2rem !important; /* 32px padding on large screens */
    }
  }

  /* Button Components */
  .btn-primary {
    background-color: var(--color-ws-primary-700);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
    border: none;
    cursor: pointer;
  }

  .btn-primary:hover {
    background-color: var(--color-ws-primary-800);
  }

  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-ws-primary-500);
  }

  .btn-secondary {
    background-color: var(--color-ws-secondary-100);
    color: var(--color-ws-secondary-900);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
    border: none;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background-color: var(--color-ws-secondary-200);
  }

  .btn-success {
    @apply bg-success-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 border-none cursor-pointer;
  }

  .btn-success:hover {
    @apply bg-success-700;
  }

  .btn-warning {
    @apply bg-warning-500 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 border-none cursor-pointer;
  }

  .btn-warning:hover {
    @apply bg-warning-600;
  }

  .btn-danger {
    @apply bg-danger-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 border-none cursor-pointer;
  }

  .btn-danger:hover {
    @apply bg-danger-700;
  }

  /* Form Components */
  .form-input {
    @apply block w-full py-2 px-3 border border-ws-secondary-300 rounded-md shadow-sm text-sm;
  }

  .form-input:focus {
    @apply outline-none border-ws-primary-500 ring-1 ring-ws-primary-500;
  }

  .form-input::placeholder {
    @apply text-ws-secondary-400;
  }

  .form-select {
    @apply block w-full py-2 px-3 border border-ws-secondary-300 rounded-md bg-white shadow-sm text-sm;
  }

  .form-select:focus {
    @apply outline-none border-ws-primary-500 ring-1 ring-ws-primary-500;
  }

  .form-textarea {
    @apply block w-full py-2 px-3 border border-ws-secondary-300 rounded-md shadow-sm text-sm resize-y;
  }

  .form-textarea:focus {
    @apply outline-none border-ws-primary-500 ring-1 ring-ws-primary-500;
  }

  .form-textarea::placeholder {
    @apply text-ws-secondary-400;
  }

  .form-label {
    @apply block text-sm font-medium text-ws-secondary-700 mb-1;
  }

  .form-error {
    @apply mt-1 text-sm text-danger-600;
  }

  .form-help {
    @apply mt-1 text-sm text-ws-secondary-500;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-md border border-ws-secondary-200;
  }

  .card-header {
    @apply p-6 border-b border-ws-secondary-200;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply p-6 border-t border-ws-secondary-200 bg-ws-secondary-50;
  }

  /* Alert Components */
  .alert {
    @apply p-4 rounded-md border;
  }

  .alert-success {
    @apply bg-success-50 border-success-200 text-success-800;
  }

  .alert-warning {
    @apply bg-warning-50 border-warning-200 text-warning-800;
  }

  .alert-danger {
    @apply bg-danger-50 border-danger-200 text-danger-800;
  }

  .alert-info {
    @apply bg-ws-secondary-50 border-ws-secondary-200 text-ws-secondary-800;
  }

  /* Navigation Components */
  .nav-link {
    @apply text-ws-secondary-600 py-2 px-3 rounded-md text-sm font-medium transition-colors duration-200 no-underline;
  }

  .nav-link:hover {
    @apply text-ws-primary-700;
  }

  .nav-link-active {
    @apply text-ws-primary-700 bg-ws-primary-50;
  }

  /* Widget Dashboard Components */
  .widget-card {
    @apply bg-white rounded-lg shadow-md border border-ws-secondary-200 p-6 transition-shadow duration-200;
  }

  .widget-card:hover {
    @apply shadow-lg;
  }

  .widget-icon {
    @apply w-12 h-12 flex items-center justify-center bg-ws-primary-100 rounded-lg;
  }

  .widget-title {
    @apply text-xl font-semibold text-ws-secondary-900 mb-2;
  }

  .widget-description {
    @apply text-ws-secondary-600 text-sm;
  }

  /* Title Divider */
  .title-divider {
    @apply text-2xl font-bold text-ws-secondary-900 mb-6 pb-2 border-b-2 border-ws-primary-700;
  }

  .title-divider span {
    @apply bg-white pr-4;
  }

  .title-divider small {
    @apply text-sm font-normal text-ws-secondary-600 block mt-1;
  }
}

/* Custom Utility Classes (project-specific) */
@layer utilities {
  .text-brand {
    @apply text-ws-primary-700;
  }

  .bg-brand {
    @apply bg-ws-primary-700;
  }

  .border-brand {
    @apply border-ws-primary-700;
  }

  .shadow-brand {
    @apply shadow-[0_4px_6px_-1px_rgb(190_62_29_/_0.1),0_2px_4px_-2px_rgb(190_62_29_/_0.1)];
  }
}

/* Custom theme variables for narrower container */
@theme {
  --breakpoint-2xl: 70rem; /* 1120px - narrower container max-width */
}

/* Legacy Bootstrap Compatibility - Keep for gradual migration */
@layer base {

  .row {
    @apply flex flex-wrap -mx-4;
  }

  .col-md-1 { @apply w-full px-4; }
  .col-md-2 { @apply w-full px-4; }
  .col-md-3 { @apply w-full px-4; }
  .col-md-4 { @apply w-full px-4; }
  .col-md-5 { @apply w-full px-4; }
  .col-md-6 { @apply w-full px-4; }
  .col-md-7 { @apply w-full px-4; }
  .col-md-8 { @apply w-full px-4; }
  .col-md-9 { @apply w-full px-4; }
  .col-md-10 { @apply w-full px-4; }
  .col-md-11 { @apply w-full px-4; }
  .col-md-12 { @apply w-full px-4; }

  @media (min-width: 768px) {
    .col-md-1 { @apply w-1/12; }
    .col-md-2 { @apply w-2/12; }
    .col-md-3 { @apply w-3/12; }
    .col-md-4 { @apply w-4/12; }
    .col-md-5 { @apply w-5/12; }
    .col-md-6 { @apply w-6/12; }
    .col-md-7 { @apply w-7/12; }
    .col-md-8 { @apply w-8/12; }
    .col-md-9 { @apply w-9/12; }
    .col-md-10 { @apply w-10/12; }
    .col-md-11 { @apply w-11/12; }
    .col-md-12 { @apply w-full; }
  }

  .col-sm-6 { @apply w-full px-4; }

  @media (min-width: 640px) {
    .col-sm-6 { @apply w-6/12; }
  }

  /* Form styling compatibility */
  .form-control {
    @apply block w-full py-2 px-3 border border-ws-secondary-300 rounded-md shadow-sm text-sm;
  }

  .form-control:focus {
    @apply outline-none border-ws-primary-500 ring-1 ring-ws-primary-500;
  }

  .form-group {
    @apply mb-4;
  }

  .control-label {
    @apply block text-sm font-medium text-ws-secondary-700 mb-1;
  }

  .has-error .form-control {
    @apply border-danger-500;
  }

  .has-error .form-control:focus {
    @apply border-danger-500 ring-danger-500;
  }

  .text-danger {
    @apply mt-1 text-sm text-danger-600;
  }

  .text-info {
    @apply mt-1 text-sm text-ws-secondary-500;
  }

  .text-success {
    @apply text-success-600;
  }

  .text-warning {
    @apply text-warning-600;
  }

  /* Button compatibility */
  .btn {
    @apply inline-flex items-center justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md transition-colors duration-200 cursor-pointer no-underline;
  }

  .btn:focus {
    @apply outline-none ring-2 ring-blue-500;
  }

  .btn-default {
    @apply bg-ws-secondary-100 text-ws-secondary-900;
  }

  .btn-default:hover {
    @apply bg-ws-secondary-200;
  }

  /* Spacing utilities - legacy support */
  .mb20 { @apply mb-5; }
  .mb40 { @apply mb-10; }
  .mb80 { @apply mb-20; }
  .mt10 { @apply mt-2.5; }
  .mt20 { @apply mt-5; }
  .mt40 { @apply mt-10; }

  /* Text utilities */
  .text-center { @apply text-center; }
  .text-left { @apply text-left; }
  .text-right { @apply text-right; }

  /* Display utilities */
  .hide { @apply hidden; }
  .show { @apply block; }

  /* Pull utilities */
  .pull-left { @apply float-left; }
  .pull-right { @apply float-right; }
}

/* Custom fractional spacing utilities (TailwindCSS v4.1 workaround) */
@layer utilities {
  /* Fractional space-between utilities */
  .space-y-0\.5 > * + * {
    margin-top: 0.125rem; /* 2px */
  }
  
  .space-y-1\.5 > * + * {
    margin-top: 0.375rem; /* 6px */
  }
  
  .space-y-2\.5 > * + * {
    margin-top: 0.625rem; /* 10px */
  }
  
  .space-x-0\.5 > * + * {
    margin-left: 0.125rem; /* 2px */
  }
  
  .space-x-1\.5 > * + * {
    margin-left: 0.375rem; /* 6px */
  }
  
  .space-x-2\.5 > * + * {
    margin-left: 0.625rem; /* 10px */
  }
  
  /* Fractional padding utilities */
  .p-0\.5 { padding: 0.125rem; }
  .px-0\.5 { 
    padding-left: 0.125rem; 
    padding-right: 0.125rem; 
  }
  .py-0\.5 { 
    padding-top: 0.125rem; 
    padding-bottom: 0.125rem; 
  }
  .pt-0\.5 { padding-top: 0.125rem; }
  .pr-0\.5 { padding-right: 0.125rem; }
  .pb-0\.5 { padding-bottom: 0.125rem; }
  .pl-0\.5 { padding-left: 0.125rem; }
  
  /* Fractional margin utilities */
  .m-0\.5 { margin: 0.125rem; }
  .mx-0\.5 { 
    margin-left: 0.125rem; 
    margin-right: 0.125rem; 
  }
  .my-0\.5 { 
    margin-top: 0.125rem; 
    margin-bottom: 0.125rem; 
  }
  .mt-0\.5 { margin-top: 0.125rem; }
  .mr-0\.5 { margin-right: 0.125rem; }
  .mb-0\.5 { margin-bottom: 0.125rem; }
  .ml-0\.5 { margin-left: 0.125rem; }
}
