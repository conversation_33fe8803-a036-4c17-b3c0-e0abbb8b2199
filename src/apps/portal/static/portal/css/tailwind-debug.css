/*! tailwindcss v4.1.8 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-orange-50: oklch(98% 0.016 73.684);
    --color-orange-200: oklch(90.1% 0.076 70.697);
    --color-orange-800: oklch(47% 0.157 37.304);
    --color-yellow-50: oklch(98.7% 0.026 102.212);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-yellow-700: oklch(55.4% 0.135 66.442);
    --color-yellow-800: oklch(47.6% 0.114 61.907);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-300: oklch(87.1% 0.15 154.449);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-700: oklch(52.7% 0.154 150.069);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-indigo-200: oklch(87% 0.065 274.039);
    --color-indigo-500: oklch(58.5% 0.233 277.117);
    --color-indigo-600: oklch(51.1% 0.262 276.966);
    --color-indigo-700: oklch(45.7% 0.24 277.023);
    --color-purple-50: oklch(97.7% 0.014 308.299);
    --color-purple-100: oklch(94.6% 0.033 307.174);
    --color-purple-200: oklch(90.2% 0.063 306.703);
    --color-purple-300: oklch(82.7% 0.119 306.383);
    --color-purple-400: oklch(71.4% 0.203 305.504);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-purple-600: oklch(55.8% 0.288 302.321);
    --color-purple-700: oklch(49.6% 0.265 301.924);
    --color-purple-800: oklch(43.8% 0.218 303.724);
    --color-purple-900: oklch(38.1% 0.176 304.987);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --breakpoint-xl: 80rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    --tracking-tight: -0.025em;
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --blur-xs: 4px;
    --blur-sm: 8px;
    --blur-3xl: 64px;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-ws-primary-50: #fef2f2;
    --color-ws-primary-100: #fee2e2;
    --color-ws-primary-200: #fecaca;
    --color-ws-primary-300: #fca5a5;
    --color-ws-primary-400: #f87171;
    --color-ws-primary-500: #ef4444;
    --color-ws-primary-600: #dc2626;
    --color-ws-primary-700: #be3e1d;
    --color-ws-primary-800: #991b1b;
    --color-ws-primary-900: #7f1d1d;
    --color-ws-secondary-50: #f8fafc;
    --color-ws-secondary-100: #f1f5f9;
    --color-ws-secondary-200: #e2e8f0;
    --color-ws-secondary-300: #cbd5e1;
    --color-ws-secondary-400: #94a3b8;
    --color-ws-secondary-500: #64748b;
    --color-ws-secondary-600: #475569;
    --color-ws-secondary-700: #334155;
    --color-ws-secondary-800: #1e293b;
    --color-ws-secondary-900: #0f172a;
    --color-success-50: #f0fdf4;
    --color-success-100: #dcfce7;
    --color-success-200: #bbf7d0;
    --color-success-300: #86efac;
    --color-success-400: #4ade80;
    --color-success-500: #22c55e;
    --color-success-600: #16a34a;
    --color-success-700: #15803d;
    --color-success-800: #166534;
    --color-success-900: #14532d;
    --color-warning-50: #fffbeb;
    --color-warning-100: #fef3c7;
    --color-warning-200: #fde68a;
    --color-warning-300: #fcd34d;
    --color-warning-400: #fbbf24;
    --color-warning-500: #f59e0b;
    --color-warning-600: #d97706;
    --color-warning-700: #b45309;
    --color-warning-800: #92400e;
    --color-warning-900: #78350f;
    --color-danger-50: #fef2f2;
    --color-danger-100: #fee2e2;
    --color-danger-200: #fecaca;
    --color-danger-300: #fca5a5;
    --color-danger-400: #f87171;
    --color-danger-500: #ef4444;
    --color-danger-600: #dc2626;
    --color-danger-700: #b91c1c;
    --color-danger-800: #991b1b;
    --color-danger-900: #7f1d1d;
    --font-family-sans: 'Open Sans', system-ui, -apple-system, sans-serif;
    --font-family-heading: 'Rambla', system-ui, -apple-system, sans-serif;
    --font-family-accent: 'Calligraffitti', cursive;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .visible\! {
    visibility: visible !important;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-top-40 {
    top: calc(var(--spacing) * -40);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-\[calc\(100\%-13rem\)\] {
    top: calc(100% - 13rem);
  }
  .top-full {
    top: 100%;
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .-left-8 {
    left: calc(var(--spacing) * -8);
  }
  .left-\[calc\(50\%\+3rem\)\] {
    left: calc(50% + 3rem);
  }
  .left-\[calc\(50\%-11rem\)\] {
    left: calc(50% - 11rem);
  }
  .isolate {
    isolation: isolate;
  }
  .-z-10 {
    z-index: calc(10 * -1);
  }
  .z-10 {
    z-index: 10;
  }
  .z-50 {
    z-index: 50;
  }
  .col-12 {
    grid-column: 12;
  }
  .col-start-1 {
    grid-column-start: 1;
  }
  .row-90 {
    grid-row: 90;
  }
  .row-start-1 {
    grid-row-start: 1;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 70rem) {
      max-width: 70rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
  }
  .-m-1\.5 {
    margin: calc(var(--spacing) * -1.5);
  }
  .-m-2\.5 {
    margin: calc(var(--spacing) * -2.5);
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .m-0\.5 {
    margin: calc(var(--spacing) * 0.5);
  }
  .m-1 {
    margin: calc(var(--spacing) * 1);
  }
  .m-1\.5 {
    margin: calc(var(--spacing) * 1.5);
  }
  .m-2 {
    margin: calc(var(--spacing) * 2);
  }
  .m-2\.5 {
    margin: calc(var(--spacing) * 2.5);
  }
  .m-3 {
    margin: calc(var(--spacing) * 3);
  }
  .m-3\.5 {
    margin: calc(var(--spacing) * 3.5);
  }
  .m-4 {
    margin: calc(var(--spacing) * 4);
  }
  .m-5 {
    margin: calc(var(--spacing) * 5);
  }
  .m-6 {
    margin: calc(var(--spacing) * 6);
  }
  .m-6\.75 {
    margin: calc(var(--spacing) * 6.75);
  }
  .m-7 {
    margin: calc(var(--spacing) * 7);
  }
  .m-8 {
    margin: calc(var(--spacing) * 8);
  }
  .m-9 {
    margin: calc(var(--spacing) * 9);
  }
  .m-10 {
    margin: calc(var(--spacing) * 10);
  }
  .m-11 {
    margin: calc(var(--spacing) * 11);
  }
  .m-12 {
    margin: calc(var(--spacing) * 12);
  }
  .m-14 {
    margin: calc(var(--spacing) * 14);
  }
  .m-16 {
    margin: calc(var(--spacing) * 16);
  }
  .m-20 {
    margin: calc(var(--spacing) * 20);
  }
  .m-24 {
    margin: calc(var(--spacing) * 24);
  }
  .m-40 {
    margin: calc(var(--spacing) * 40);
  }
  .m-55 {
    margin: calc(var(--spacing) * 55);
  }
  .m-71 {
    margin: calc(var(--spacing) * 71);
  }
  .m-82 {
    margin: calc(var(--spacing) * 82);
  }
  .m-90 {
    margin: calc(var(--spacing) * 90);
  }
  .m-109 {
    margin: calc(var(--spacing) * 109);
  }
  .m-128 {
    margin: calc(var(--spacing) * 128);
  }
  .m-178 {
    margin: calc(var(--spacing) * 178);
  }
  .m-186\.5 {
    margin: calc(var(--spacing) * 186.5);
  }
  .m-214 {
    margin: calc(var(--spacing) * 214);
  }
  .m-357 {
    margin: calc(var(--spacing) * 357);
  }
  .m-374 {
    margin: calc(var(--spacing) * 374);
  }
  .m-375 {
    margin: calc(var(--spacing) * 375);
  }
  .m-490 {
    margin: calc(var(--spacing) * 490);
  }
  .m-572 {
    margin: calc(var(--spacing) * 572);
  }
  .m-600 {
    margin: calc(var(--spacing) * 600);
  }
  .m-620 {
    margin: calc(var(--spacing) * 620);
  }
  .m-753 {
    margin: calc(var(--spacing) * 753);
  }
  .m-1059 {
    margin: calc(var(--spacing) * 1059);
  }
  .m-1999\.5 {
    margin: calc(var(--spacing) * 1999.5);
  }
  .m-2085 {
    margin: calc(var(--spacing) * 2085);
  }
  .m-auto {
    margin: auto;
  }
  .m-px {
    margin: 1px;
  }
  .-mx-3 {
    margin-inline: calc(var(--spacing) * -3);
  }
  .mx-0 {
    margin-inline: calc(var(--spacing) * 0);
  }
  .mx-0\.5 {
    margin-inline: calc(var(--spacing) * 0.5);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-1\.5 {
    margin-inline: calc(var(--spacing) * 1.5);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-2\.5 {
    margin-inline: calc(var(--spacing) * 2.5);
  }
  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }
  .mx-3\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }
  .mx-6 {
    margin-inline: calc(var(--spacing) * 6);
  }
  .mx-7 {
    margin-inline: calc(var(--spacing) * 7);
  }
  .mx-8 {
    margin-inline: calc(var(--spacing) * 8);
  }
  .mx-9 {
    margin-inline: calc(var(--spacing) * 9);
  }
  .mx-10 {
    margin-inline: calc(var(--spacing) * 10);
  }
  .mx-11 {
    margin-inline: calc(var(--spacing) * 11);
  }
  .mx-12 {
    margin-inline: calc(var(--spacing) * 12);
  }
  .mx-14 {
    margin-inline: calc(var(--spacing) * 14);
  }
  .mx-16 {
    margin-inline: calc(var(--spacing) * 16);
  }
  .mx-20 {
    margin-inline: calc(var(--spacing) * 20);
  }
  .mx-24 {
    margin-inline: calc(var(--spacing) * 24);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .mx-px {
    margin-inline: 1px;
  }
  .-my-6 {
    margin-block: calc(var(--spacing) * -6);
  }
  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }
  .my-0\.5 {
    margin-block: calc(var(--spacing) * 0.5);
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .my-1\.5 {
    margin-block: calc(var(--spacing) * 1.5);
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-2\.5 {
    margin-block: calc(var(--spacing) * 2.5);
  }
  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }
  .my-3\.5 {
    margin-block: calc(var(--spacing) * 3.5);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-7 {
    margin-block: calc(var(--spacing) * 7);
  }
  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }
  .my-9 {
    margin-block: calc(var(--spacing) * 9);
  }
  .my-10 {
    margin-block: calc(var(--spacing) * 10);
  }
  .my-11 {
    margin-block: calc(var(--spacing) * 11);
  }
  .my-12 {
    margin-block: calc(var(--spacing) * 12);
  }
  .my-14 {
    margin-block: calc(var(--spacing) * 14);
  }
  .my-16 {
    margin-block: calc(var(--spacing) * 16);
  }
  .my-20 {
    margin-block: calc(var(--spacing) * 20);
  }
  .my-24 {
    margin-block: calc(var(--spacing) * 24);
  }
  .my-px {
    margin-block: 1px;
  }
  .-mt-8 {
    margin-top: calc(var(--spacing) * -8);
  }
  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-2\.5 {
    margin-top: calc(var(--spacing) * 2.5);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-3\.5 {
    margin-top: calc(var(--spacing) * 3.5);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-7 {
    margin-top: calc(var(--spacing) * 7);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-9 {
    margin-top: calc(var(--spacing) * 9);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-11 {
    margin-top: calc(var(--spacing) * 11);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-14 {
    margin-top: calc(var(--spacing) * 14);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }
  .mt-40 {
    margin-top: calc(var(--spacing) * 40);
  }
  .mt-auto {
    margin-top: auto;
  }
  .mt-px {
    margin-top: 1px;
  }
  .mr-0 {
    margin-right: calc(var(--spacing) * 0);
  }
  .mr-0\.5 {
    margin-right: calc(var(--spacing) * 0.5);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-2\.5 {
    margin-right: calc(var(--spacing) * 2.5);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-3\.5 {
    margin-right: calc(var(--spacing) * 3.5);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-5 {
    margin-right: calc(var(--spacing) * 5);
  }
  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }
  .mr-7 {
    margin-right: calc(var(--spacing) * 7);
  }
  .mr-8 {
    margin-right: calc(var(--spacing) * 8);
  }
  .mr-9 {
    margin-right: calc(var(--spacing) * 9);
  }
  .mr-10 {
    margin-right: calc(var(--spacing) * 10);
  }
  .mr-11 {
    margin-right: calc(var(--spacing) * 11);
  }
  .mr-12 {
    margin-right: calc(var(--spacing) * 12);
  }
  .mr-14 {
    margin-right: calc(var(--spacing) * 14);
  }
  .mr-16 {
    margin-right: calc(var(--spacing) * 16);
  }
  .mr-20 {
    margin-right: calc(var(--spacing) * 20);
  }
  .mr-24 {
    margin-right: calc(var(--spacing) * 24);
  }
  .mr-auto {
    margin-right: auto;
  }
  .mr-px {
    margin-right: 1px;
  }
  .-mb-px {
    margin-bottom: -1px;
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * 0.5);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-3\.5 {
    margin-bottom: calc(var(--spacing) * 3.5);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-7 {
    margin-bottom: calc(var(--spacing) * 7);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-9 {
    margin-bottom: calc(var(--spacing) * 9);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-11 {
    margin-bottom: calc(var(--spacing) * 11);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-14 {
    margin-bottom: calc(var(--spacing) * 14);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }
  .mb-24 {
    margin-bottom: calc(var(--spacing) * 24);
  }
  .mb-auto {
    margin-bottom: auto;
  }
  .mb-px {
    margin-bottom: 1px;
  }
  .-ml-0\.5 {
    margin-left: calc(var(--spacing) * -0.5);
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .ml-0 {
    margin-left: calc(var(--spacing) * 0);
  }
  .ml-0\.5 {
    margin-left: calc(var(--spacing) * 0.5);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-1\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-2\.5 {
    margin-left: calc(var(--spacing) * 2.5);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-3\.5 {
    margin-left: calc(var(--spacing) * 3.5);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-5 {
    margin-left: calc(var(--spacing) * 5);
  }
  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }
  .ml-7 {
    margin-left: calc(var(--spacing) * 7);
  }
  .ml-8 {
    margin-left: calc(var(--spacing) * 8);
  }
  .ml-9 {
    margin-left: calc(var(--spacing) * 9);
  }
  .ml-10 {
    margin-left: calc(var(--spacing) * 10);
  }
  .ml-11 {
    margin-left: calc(var(--spacing) * 11);
  }
  .ml-12 {
    margin-left: calc(var(--spacing) * 12);
  }
  .ml-14 {
    margin-left: calc(var(--spacing) * 14);
  }
  .ml-16 {
    margin-left: calc(var(--spacing) * 16);
  }
  .ml-20 {
    margin-left: calc(var(--spacing) * 20);
  }
  .ml-24 {
    margin-left: calc(var(--spacing) * 24);
  }
  .ml-auto {
    margin-left: auto;
  }
  .ml-px {
    margin-left: 1px;
  }
  .\!hidden {
    display: none !important;
  }
  .\!inline {
    display: inline !important;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .flow-root {
    display: flow-root;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .inline-grid {
    display: inline-grid;
  }
  .inline-table {
    display: inline-table;
  }
  .list-item {
    display: list-item;
  }
  .table {
    display: table;
  }
  .table-caption {
    display: table-caption;
  }
  .table-cell {
    display: table-cell;
  }
  .table-column {
    display: table-column;
  }
  .table-column-group {
    display: table-column-group;
  }
  .table-footer-group {
    display: table-footer-group;
  }
  .table-header-group {
    display: table-header-group;
  }
  .table-row {
    display: table-row;
  }
  .table-row-group {
    display: table-row-group;
  }
  .aspect-1155\/678 {
    aspect-ratio: 1155/678;
  }
  .size-0 {
    width: calc(var(--spacing) * 0);
    height: calc(var(--spacing) * 0);
  }
  .size-0\.5 {
    width: calc(var(--spacing) * 0.5);
    height: calc(var(--spacing) * 0.5);
  }
  .size-1 {
    width: calc(var(--spacing) * 1);
    height: calc(var(--spacing) * 1);
  }
  .size-1\.5 {
    width: calc(var(--spacing) * 1.5);
    height: calc(var(--spacing) * 1.5);
  }
  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }
  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }
  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }
  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }
  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }
  .size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }
  .size-11 {
    width: calc(var(--spacing) * 11);
    height: calc(var(--spacing) * 11);
  }
  .size-12 {
    width: calc(var(--spacing) * 12);
    height: calc(var(--spacing) * 12);
  }
  .size-16 {
    width: calc(var(--spacing) * 16);
    height: calc(var(--spacing) * 16);
  }
  .size-20 {
    width: calc(var(--spacing) * 20);
    height: calc(var(--spacing) * 20);
  }
  .size-24 {
    width: calc(var(--spacing) * 24);
    height: calc(var(--spacing) * 24);
  }
  .size-32 {
    width: calc(var(--spacing) * 32);
    height: calc(var(--spacing) * 32);
  }
  .size-40 {
    width: calc(var(--spacing) * 40);
    height: calc(var(--spacing) * 40);
  }
  .size-48 {
    width: calc(var(--spacing) * 48);
    height: calc(var(--spacing) * 48);
  }
  .size-56 {
    width: calc(var(--spacing) * 56);
    height: calc(var(--spacing) * 56);
  }
  .size-64 {
    width: calc(var(--spacing) * 64);
    height: calc(var(--spacing) * 64);
  }
  .size-72 {
    width: calc(var(--spacing) * 72);
    height: calc(var(--spacing) * 72);
  }
  .size-80 {
    width: calc(var(--spacing) * 80);
    height: calc(var(--spacing) * 80);
  }
  .size-96 {
    width: calc(var(--spacing) * 96);
    height: calc(var(--spacing) * 96);
  }
  .size-auto {
    width: auto;
    height: auto;
  }
  .size-full {
    width: 100%;
    height: 100%;
  }
  .size-px {
    width: 1px;
    height: 1px;
  }
  .h-0 {
    height: calc(var(--spacing) * 0);
  }
  .h-0\.5 {
    height: calc(var(--spacing) * 0.5);
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-1\/2 {
    height: calc(1/2 * 100%);
  }
  .h-1\/3 {
    height: calc(1/3 * 100%);
  }
  .h-1\/4 {
    height: calc(1/4 * 100%);
  }
  .h-1\/5 {
    height: calc(1/5 * 100%);
  }
  .h-1\/6 {
    height: calc(1/6 * 100%);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-2\/3 {
    height: calc(2/3 * 100%);
  }
  .h-2\/4 {
    height: calc(2/4 * 100%);
  }
  .h-2\/5 {
    height: calc(2/5 * 100%);
  }
  .h-2\/6 {
    height: calc(2/6 * 100%);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }
  .h-3\/4 {
    height: calc(3/4 * 100%);
  }
  .h-3\/5 {
    height: calc(3/5 * 100%);
  }
  .h-3\/6 {
    height: calc(3/6 * 100%);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-4\/5 {
    height: calc(4/5 * 100%);
  }
  .h-4\/6 {
    height: calc(4/6 * 100%);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-5\/6 {
    height: calc(5/6 * 100%);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-56 {
    height: calc(var(--spacing) * 56);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-72 {
    height: calc(var(--spacing) * 72);
  }
  .h-80 {
    height: calc(var(--spacing) * 80);
  }
  .h-96 {
    height: calc(var(--spacing) * 96);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-px {
    height: 1px;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-56 {
    max-height: calc(var(--spacing) * 56);
  }
  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }
  .w-0 {
    width: calc(var(--spacing) * 0);
  }
  .w-0\.5 {
    width: calc(var(--spacing) * 0.5);
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-1\/4 {
    width: calc(1/4 * 100%);
  }
  .w-1\/5 {
    width: calc(1/5 * 100%);
  }
  .w-1\/6 {
    width: calc(1/6 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }
  .w-2\/3 {
    width: calc(2/3 * 100%);
  }
  .w-2\/4 {
    width: calc(2/4 * 100%);
  }
  .w-2\/5 {
    width: calc(2/5 * 100%);
  }
  .w-2\/6 {
    width: calc(2/6 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-3\/4 {
    width: calc(3/4 * 100%);
  }
  .w-3\/5 {
    width: calc(3/5 * 100%);
  }
  .w-3\/6 {
    width: calc(3/6 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-4\/5 {
    width: calc(4/5 * 100%);
  }
  .w-4\/6 {
    width: calc(4/6 * 100%);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-5\/6 {
    width: calc(5/6 * 100%);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-56 {
    width: calc(var(--spacing) * 56);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-\[36\.125rem\] {
    width: 36.125rem;
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .w-px {
    width: 1px;
  }
  .w-screen {
    width: 100vw;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-\[32px\] {
    min-width: 32px;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-auto {
    flex: auto;
  }
  .flex-none {
    flex: none;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .shrink {
    flex-shrink: 1;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .grow {
    flex-grow: 1;
  }
  .grow-0 {
    flex-grow: 0;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .rotate-\[30deg\] {
    rotate: 30deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .transform-gpu {
    transform: translateZ(0) var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-default {
    cursor: default;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
  .grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }
  .grid-cols-9 {
    grid-template-columns: repeat(9, minmax(0, 1fr));
  }
  .grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }
  .grid-cols-11 {
    grid-template-columns: repeat(11, minmax(0, 1fr));
  }
  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .flex-wrap-reverse {
    flex-wrap: wrap-reverse;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-baseline-last {
    align-items: last baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .items-stretch {
    align-items: stretch;
  }
  .justify-around {
    justify-content: space-around;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-center-safe {
    justify-content: safe center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-evenly {
    justify-content: space-evenly;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }
  .gap-0\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-3\.5 {
    gap: calc(var(--spacing) * 3.5);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-7 {
    gap: calc(var(--spacing) * 7);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-9 {
    gap: calc(var(--spacing) * 9);
  }
  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }
  .gap-11 {
    gap: calc(var(--spacing) * 11);
  }
  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }
  .gap-14 {
    gap: calc(var(--spacing) * 14);
  }
  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }
  .gap-20 {
    gap: calc(var(--spacing) * 20);
  }
  .gap-24 {
    gap: calc(var(--spacing) * 24);
  }
  .gap-px {
    gap: 1px;
  }
  .space-y-0 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-0\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-7 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 7) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 7) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-9 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 9) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 9) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-10 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 10) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-11 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 11) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 11) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-12 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-14 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 14) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 14) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-16 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-20 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 20) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 20) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-24 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 24) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 24) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-px {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(1px * var(--tw-space-y-reverse));
      margin-block-end: calc(1px * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-reverse {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 1;
    }
  }
  .gap-x-1 {
    column-gap: calc(var(--spacing) * 1);
  }
  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }
  .space-x-0 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 0) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-0\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-6 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-7 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 7) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 7) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-9 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 9) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 9) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-10 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 10) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-11 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 11) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 11) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-12 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 12) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-14 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 14) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 14) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-16 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 16) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-20 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 20) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 20) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-24 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 24) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 24) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-px {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(1px * var(--tw-space-x-reverse));
      margin-inline-end: calc(1px * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-reverse {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 1;
    }
  }
  .divide-x {
    :where(& > :not(:last-child)) {
      --tw-divide-x-reverse: 0;
      border-inline-style: var(--tw-border-style);
      border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
      border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .divide-gray-300 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-300);
    }
  }
  .divide-gray-400 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-400);
    }
  }
  .divide-gray-500\/10 {
    :where(& > :not(:last-child)) {
      border-color: color-mix(in srgb, oklch(55.1% 0.027 264.364) 10%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--color-gray-500) 10%, transparent);
      }
    }
  }
  .self-baseline-last {
    align-self: last baseline;
  }
  .self-center {
    align-self: center;
  }
  .justify-self-end {
    justify-self: flex-end;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-none {
    border-radius: 0;
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-xs {
    border-radius: var(--radius-xs);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-8 {
    border-style: var(--tw-border-style);
    border-width: 8px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-t-0 {
    border-top-style: var(--tw-border-style);
    border-top-width: 0px;
  }
  .border-t-2 {
    border-top-style: var(--tw-border-style);
    border-top-width: 2px;
  }
  .border-t-4 {
    border-top-style: var(--tw-border-style);
    border-top-width: 4px;
  }
  .border-t-8 {
    border-top-style: var(--tw-border-style);
    border-top-width: 8px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-r-0 {
    border-right-style: var(--tw-border-style);
    border-right-width: 0px;
  }
  .border-r-2 {
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }
  .border-r-4 {
    border-right-style: var(--tw-border-style);
    border-right-width: 4px;
  }
  .border-r-8 {
    border-right-style: var(--tw-border-style);
    border-right-width: 8px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-0 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-b-4 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 4px;
  }
  .border-b-8 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 8px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-l-0 {
    border-left-style: var(--tw-border-style);
    border-left-width: 0px;
  }
  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-l-8 {
    border-left-style: var(--tw-border-style);
    border-left-width: 8px;
  }
  .border-black {
    border-color: var(--color-black);
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-blue-300 {
    border-color: var(--color-blue-300);
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-blue-700 {
    border-color: var(--color-blue-700);
  }
  .border-danger-50 {
    border-color: var(--color-danger-50);
  }
  .border-danger-100 {
    border-color: var(--color-danger-100);
  }
  .border-danger-200 {
    border-color: var(--color-danger-200);
  }
  .border-danger-300 {
    border-color: var(--color-danger-300);
  }
  .border-danger-400 {
    border-color: var(--color-danger-400);
  }
  .border-danger-500 {
    border-color: var(--color-danger-500);
  }
  .border-danger-600 {
    border-color: var(--color-danger-600);
  }
  .border-danger-700 {
    border-color: var(--color-danger-700);
  }
  .border-danger-800 {
    border-color: var(--color-danger-800);
  }
  .border-danger-900 {
    border-color: var(--color-danger-900);
  }
  .border-gray-50 {
    border-color: var(--color-gray-50);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-gray-400 {
    border-color: var(--color-gray-400);
  }
  .border-gray-500 {
    border-color: var(--color-gray-500);
  }
  .border-gray-600 {
    border-color: var(--color-gray-600);
  }
  .border-gray-700 {
    border-color: var(--color-gray-700);
  }
  .border-gray-800 {
    border-color: var(--color-gray-800);
  }
  .border-gray-900 {
    border-color: var(--color-gray-900);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-green-300 {
    border-color: var(--color-green-300);
  }
  .border-green-600 {
    border-color: var(--color-green-600);
  }
  .border-green-700 {
    border-color: var(--color-green-700);
  }
  .border-indigo-500 {
    border-color: var(--color-indigo-500);
  }
  .border-orange-200 {
    border-color: var(--color-orange-200);
  }
  .border-purple-200 {
    border-color: var(--color-purple-200);
  }
  .border-purple-300 {
    border-color: var(--color-purple-300);
  }
  .border-purple-600 {
    border-color: var(--color-purple-600);
  }
  .border-purple-700 {
    border-color: var(--color-purple-700);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-300 {
    border-color: var(--color-red-300);
  }
  .border-red-600 {
    border-color: var(--color-red-600);
  }
  .border-red-700 {
    border-color: var(--color-red-700);
  }
  .border-success-50 {
    border-color: var(--color-success-50);
  }
  .border-success-100 {
    border-color: var(--color-success-100);
  }
  .border-success-200 {
    border-color: var(--color-success-200);
  }
  .border-success-300 {
    border-color: var(--color-success-300);
  }
  .border-success-400 {
    border-color: var(--color-success-400);
  }
  .border-success-500 {
    border-color: var(--color-success-500);
  }
  .border-success-600 {
    border-color: var(--color-success-600);
  }
  .border-success-700 {
    border-color: var(--color-success-700);
  }
  .border-success-800 {
    border-color: var(--color-success-800);
  }
  .border-success-900 {
    border-color: var(--color-success-900);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-warning-50 {
    border-color: var(--color-warning-50);
  }
  .border-warning-100 {
    border-color: var(--color-warning-100);
  }
  .border-warning-200 {
    border-color: var(--color-warning-200);
  }
  .border-warning-300 {
    border-color: var(--color-warning-300);
  }
  .border-warning-400 {
    border-color: var(--color-warning-400);
  }
  .border-warning-500 {
    border-color: var(--color-warning-500);
  }
  .border-warning-600 {
    border-color: var(--color-warning-600);
  }
  .border-warning-700 {
    border-color: var(--color-warning-700);
  }
  .border-warning-800 {
    border-color: var(--color-warning-800);
  }
  .border-warning-900 {
    border-color: var(--color-warning-900);
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-ws-primary-50 {
    border-color: var(--color-ws-primary-50);
  }
  .border-ws-primary-100 {
    border-color: var(--color-ws-primary-100);
  }
  .border-ws-primary-200 {
    border-color: var(--color-ws-primary-200);
  }
  .border-ws-primary-300 {
    border-color: var(--color-ws-primary-300);
  }
  .border-ws-primary-400 {
    border-color: var(--color-ws-primary-400);
  }
  .border-ws-primary-500 {
    border-color: var(--color-ws-primary-500);
  }
  .border-ws-primary-600 {
    border-color: var(--color-ws-primary-600);
  }
  .border-ws-primary-700 {
    border-color: var(--color-ws-primary-700);
  }
  .border-ws-primary-800 {
    border-color: var(--color-ws-primary-800);
  }
  .border-ws-primary-900 {
    border-color: var(--color-ws-primary-900);
  }
  .border-ws-secondary-50 {
    border-color: var(--color-ws-secondary-50);
  }
  .border-ws-secondary-100 {
    border-color: var(--color-ws-secondary-100);
  }
  .border-ws-secondary-200 {
    border-color: var(--color-ws-secondary-200);
  }
  .border-ws-secondary-300 {
    border-color: var(--color-ws-secondary-300);
  }
  .border-ws-secondary-400 {
    border-color: var(--color-ws-secondary-400);
  }
  .border-ws-secondary-500 {
    border-color: var(--color-ws-secondary-500);
  }
  .border-ws-secondary-600 {
    border-color: var(--color-ws-secondary-600);
  }
  .border-ws-secondary-700 {
    border-color: var(--color-ws-secondary-700);
  }
  .border-ws-secondary-800 {
    border-color: var(--color-ws-secondary-800);
  }
  .border-ws-secondary-900 {
    border-color: var(--color-ws-secondary-900);
  }
  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-200 {
    background-color: var(--color-blue-200);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-blue-700 {
    background-color: var(--color-blue-700);
  }
  .bg-danger-50 {
    background-color: var(--color-danger-50);
  }
  .bg-danger-100 {
    background-color: var(--color-danger-100);
  }
  .bg-danger-200 {
    background-color: var(--color-danger-200);
  }
  .bg-danger-300 {
    background-color: var(--color-danger-300);
  }
  .bg-danger-400 {
    background-color: var(--color-danger-400);
  }
  .bg-danger-500 {
    background-color: var(--color-danger-500);
  }
  .bg-danger-600 {
    background-color: var(--color-danger-600);
  }
  .bg-danger-700 {
    background-color: var(--color-danger-700);
  }
  .bg-danger-800 {
    background-color: var(--color-danger-800);
  }
  .bg-danger-900 {
    background-color: var(--color-danger-900);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }
  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }
  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-200 {
    background-color: var(--color-green-200);
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-green-700 {
    background-color: var(--color-green-700);
  }
  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }
  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }
  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-purple-200 {
    background-color: var(--color-purple-200);
  }
  .bg-purple-300 {
    background-color: var(--color-purple-300);
  }
  .bg-purple-400 {
    background-color: var(--color-purple-400);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }
  .bg-purple-700 {
    background-color: var(--color-purple-700);
  }
  .bg-purple-800 {
    background-color: var(--color-purple-800);
  }
  .bg-purple-900 {
    background-color: var(--color-purple-900);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-200 {
    background-color: var(--color-red-200);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-red-700 {
    background-color: var(--color-red-700);
  }
  .bg-success-50 {
    background-color: var(--color-success-50);
  }
  .bg-success-100 {
    background-color: var(--color-success-100);
  }
  .bg-success-200 {
    background-color: var(--color-success-200);
  }
  .bg-success-300 {
    background-color: var(--color-success-300);
  }
  .bg-success-400 {
    background-color: var(--color-success-400);
  }
  .bg-success-500 {
    background-color: var(--color-success-500);
  }
  .bg-success-600 {
    background-color: var(--color-success-600);
  }
  .bg-success-700 {
    background-color: var(--color-success-700);
  }
  .bg-success-800 {
    background-color: var(--color-success-800);
  }
  .bg-success-900 {
    background-color: var(--color-success-900);
  }
  .bg-warning-50 {
    background-color: var(--color-warning-50);
  }
  .bg-warning-100 {
    background-color: var(--color-warning-100);
  }
  .bg-warning-200 {
    background-color: var(--color-warning-200);
  }
  .bg-warning-300 {
    background-color: var(--color-warning-300);
  }
  .bg-warning-400 {
    background-color: var(--color-warning-400);
  }
  .bg-warning-500 {
    background-color: var(--color-warning-500);
  }
  .bg-warning-600 {
    background-color: var(--color-warning-600);
  }
  .bg-warning-700 {
    background-color: var(--color-warning-700);
  }
  .bg-warning-800 {
    background-color: var(--color-warning-800);
  }
  .bg-warning-900 {
    background-color: var(--color-warning-900);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-ws-primary-50 {
    background-color: var(--color-ws-primary-50);
  }
  .bg-ws-primary-100 {
    background-color: var(--color-ws-primary-100);
  }
  .bg-ws-primary-200 {
    background-color: var(--color-ws-primary-200);
  }
  .bg-ws-primary-300 {
    background-color: var(--color-ws-primary-300);
  }
  .bg-ws-primary-400 {
    background-color: var(--color-ws-primary-400);
  }
  .bg-ws-primary-500 {
    background-color: var(--color-ws-primary-500);
  }
  .bg-ws-primary-600 {
    background-color: var(--color-ws-primary-600);
  }
  .bg-ws-primary-700 {
    background-color: var(--color-ws-primary-700);
  }
  .bg-ws-primary-800 {
    background-color: var(--color-ws-primary-800);
  }
  .bg-ws-primary-900 {
    background-color: var(--color-ws-primary-900);
  }
  .bg-ws-secondary-50 {
    background-color: var(--color-ws-secondary-50);
  }
  .bg-ws-secondary-100 {
    background-color: var(--color-ws-secondary-100);
  }
  .bg-ws-secondary-200 {
    background-color: var(--color-ws-secondary-200);
  }
  .bg-ws-secondary-300 {
    background-color: var(--color-ws-secondary-300);
  }
  .bg-ws-secondary-400 {
    background-color: var(--color-ws-secondary-400);
  }
  .bg-ws-secondary-500 {
    background-color: var(--color-ws-secondary-500);
  }
  .bg-ws-secondary-600 {
    background-color: var(--color-ws-secondary-600);
  }
  .bg-ws-secondary-700 {
    background-color: var(--color-ws-secondary-700);
  }
  .bg-ws-secondary-800 {
    background-color: var(--color-ws-secondary-800);
  }
  .bg-ws-secondary-900 {
    background-color: var(--color-ws-secondary-900);
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-linear-to-tr {
    --tw-gradient-position: to top right;
    @supports (background-image: linear-gradient(in lab, red, red)) {
      --tw-gradient-position: to top right in oklab;
    }
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .via-none {
    --tw-gradient-via-stops: initial;
  }
  .from-\[\#ff80b5\] {
    --tw-gradient-from: #ff80b5;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#9089fc\] {
    --tw-gradient-to: #9089fc;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .fill-gray-500 {
    fill: var(--color-gray-500);
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-3\.5 {
    padding: calc(var(--spacing) * 3.5);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-7 {
    padding: calc(var(--spacing) * 7);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-9 {
    padding: calc(var(--spacing) * 9);
  }
  .p-10 {
    padding: calc(var(--spacing) * 10);
  }
  .p-11 {
    padding: calc(var(--spacing) * 11);
  }
  .p-12 {
    padding: calc(var(--spacing) * 12);
  }
  .p-14 {
    padding: calc(var(--spacing) * 14);
  }
  .p-16 {
    padding: calc(var(--spacing) * 16);
  }
  .p-18 {
    padding: calc(var(--spacing) * 18);
  }
  .p-20 {
    padding: calc(var(--spacing) * 20);
  }
  .p-21 {
    padding: calc(var(--spacing) * 21);
  }
  .p-24 {
    padding: calc(var(--spacing) * 24);
  }
  .p-28 {
    padding: calc(var(--spacing) * 28);
  }
  .p-32 {
    padding: calc(var(--spacing) * 32);
  }
  .p-px {
    padding: 1px;
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-0\.5 {
    padding-inline: calc(var(--spacing) * 0.5);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-3\.5 {
    padding-inline: calc(var(--spacing) * 3.5);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-7 {
    padding-inline: calc(var(--spacing) * 7);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-9 {
    padding-inline: calc(var(--spacing) * 9);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .px-11 {
    padding-inline: calc(var(--spacing) * 11);
  }
  .px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }
  .px-14 {
    padding-inline: calc(var(--spacing) * 14);
  }
  .px-16 {
    padding-inline: calc(var(--spacing) * 16);
  }
  .px-20 {
    padding-inline: calc(var(--spacing) * 20);
  }
  .px-24 {
    padding-inline: calc(var(--spacing) * 24);
  }
  .px-px {
    padding-inline: 1px;
  }
  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-3\.5 {
    padding-block: calc(var(--spacing) * 3.5);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-7 {
    padding-block: calc(var(--spacing) * 7);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-9 {
    padding-block: calc(var(--spacing) * 9);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-11 {
    padding-block: calc(var(--spacing) * 11);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-14 {
    padding-block: calc(var(--spacing) * 14);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }
  .py-32 {
    padding-block: calc(var(--spacing) * 32);
  }
  .py-px {
    padding-block: 1px;
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-14 {
    padding-top: calc(var(--spacing) * 14);
  }
  .pt-24 {
    padding-top: calc(var(--spacing) * 24);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pr-6 {
    padding-right: calc(var(--spacing) * 6);
  }
  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }
  .pr-9 {
    padding-right: calc(var(--spacing) * 9);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .text-center {
    text-align: center;
  }
  .text-justify {
    text-align: justify;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .font-mono {
    font-family: var(--font-mono);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-base\/7 {
    font-size: var(--text-base);
    line-height: calc(var(--spacing) * 7);
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-sm\/6 {
    font-size: var(--text-sm);
    line-height: calc(var(--spacing) * 6);
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .font-black {
    --tw-font-weight: var(--font-weight-black);
    font-weight: var(--font-weight-black);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }
  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .font-thin {
    --tw-font-weight: var(--font-weight-thin);
    font-weight: var(--font-weight-thin);
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .text-balance {
    text-wrap: balance;
  }
  .text-nowrap {
    text-wrap: nowrap;
  }
  .text-pretty {
    text-wrap: pretty;
  }
  .wrap-anywhere {
    overflow-wrap: anywhere;
  }
  .wrap-break-word {
    overflow-wrap: break-word;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-400 {
    color: var(--color-blue-400);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-danger-50 {
    color: var(--color-danger-50);
  }
  .text-danger-100 {
    color: var(--color-danger-100);
  }
  .text-danger-200 {
    color: var(--color-danger-200);
  }
  .text-danger-300 {
    color: var(--color-danger-300);
  }
  .text-danger-400 {
    color: var(--color-danger-400);
  }
  .text-danger-500 {
    color: var(--color-danger-500);
  }
  .text-danger-600 {
    color: var(--color-danger-600);
  }
  .text-danger-700 {
    color: var(--color-danger-700);
  }
  .text-danger-800 {
    color: var(--color-danger-800);
  }
  .text-danger-900 {
    color: var(--color-danger-900);
  }
  .text-gray-50 {
    color: var(--color-gray-50);
  }
  .text-gray-100 {
    color: var(--color-gray-100);
  }
  .text-gray-200 {
    color: var(--color-gray-200);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-400 {
    color: var(--color-green-400);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-200 {
    color: var(--color-indigo-200);
  }
  .text-indigo-500 {
    color: var(--color-indigo-500);
  }
  .text-indigo-600 {
    color: var(--color-indigo-600);
  }
  .text-orange-800 {
    color: var(--color-orange-800);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-purple-700 {
    color: var(--color-purple-700);
  }
  .text-purple-800 {
    color: var(--color-purple-800);
  }
  .text-purple-900 {
    color: var(--color-purple-900);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-success-50 {
    color: var(--color-success-50);
  }
  .text-success-100 {
    color: var(--color-success-100);
  }
  .text-success-200 {
    color: var(--color-success-200);
  }
  .text-success-300 {
    color: var(--color-success-300);
  }
  .text-success-400 {
    color: var(--color-success-400);
  }
  .text-success-500 {
    color: var(--color-success-500);
  }
  .text-success-600 {
    color: var(--color-success-600);
  }
  .text-success-700 {
    color: var(--color-success-700);
  }
  .text-success-800 {
    color: var(--color-success-800);
  }
  .text-success-900 {
    color: var(--color-success-900);
  }
  .text-warning-50 {
    color: var(--color-warning-50);
  }
  .text-warning-100 {
    color: var(--color-warning-100);
  }
  .text-warning-200 {
    color: var(--color-warning-200);
  }
  .text-warning-300 {
    color: var(--color-warning-300);
  }
  .text-warning-400 {
    color: var(--color-warning-400);
  }
  .text-warning-500 {
    color: var(--color-warning-500);
  }
  .text-warning-600 {
    color: var(--color-warning-600);
  }
  .text-warning-700 {
    color: var(--color-warning-700);
  }
  .text-warning-800 {
    color: var(--color-warning-800);
  }
  .text-warning-900 {
    color: var(--color-warning-900);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-ws-primary-50 {
    color: var(--color-ws-primary-50);
  }
  .text-ws-primary-100 {
    color: var(--color-ws-primary-100);
  }
  .text-ws-primary-200 {
    color: var(--color-ws-primary-200);
  }
  .text-ws-primary-300 {
    color: var(--color-ws-primary-300);
  }
  .text-ws-primary-400 {
    color: var(--color-ws-primary-400);
  }
  .text-ws-primary-500 {
    color: var(--color-ws-primary-500);
  }
  .text-ws-primary-600 {
    color: var(--color-ws-primary-600);
  }
  .text-ws-primary-700 {
    color: var(--color-ws-primary-700);
  }
  .text-ws-primary-800 {
    color: var(--color-ws-primary-800);
  }
  .text-ws-primary-900 {
    color: var(--color-ws-primary-900);
  }
  .text-ws-secondary-50 {
    color: var(--color-ws-secondary-50);
  }
  .text-ws-secondary-100 {
    color: var(--color-ws-secondary-100);
  }
  .text-ws-secondary-200 {
    color: var(--color-ws-secondary-200);
  }
  .text-ws-secondary-300 {
    color: var(--color-ws-secondary-300);
  }
  .text-ws-secondary-400 {
    color: var(--color-ws-secondary-400);
  }
  .text-ws-secondary-500 {
    color: var(--color-ws-secondary-500);
  }
  .text-ws-secondary-600 {
    color: var(--color-ws-secondary-600);
  }
  .text-ws-secondary-700 {
    color: var(--color-ws-secondary-700);
  }
  .text-ws-secondary-800 {
    color: var(--color-ws-secondary-800);
  }
  .text-ws-secondary-900 {
    color: var(--color-ws-secondary-900);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-600 {
    color: var(--color-yellow-600);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .overline {
    text-decoration-line: overline;
  }
  .underline {
    text-decoration-line: underline;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-25 {
    opacity: 25%;
  }
  .opacity-30 {
    opacity: 30%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow\! {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)) !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-0 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-3 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-4 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-8 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-black {
    --tw-ring-color: var(--color-black);
  }
  .ring-black\/5 {
    --tw-ring-color: color-mix(in srgb, #000 5%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-ring-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }
  .ring-gray-300 {
    --tw-ring-color: var(--color-gray-300);
  }
  .ring-gray-400 {
    --tw-ring-color: var(--color-gray-400);
  }
  .ring-gray-500 {
    --tw-ring-color: var(--color-gray-500);
  }
  .ring-gray-900\/5 {
    --tw-ring-color: color-mix(in srgb, oklch(21% 0.034 264.665) 5%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-ring-color: color-mix(in oklab, var(--color-gray-900) 5%, transparent);
    }
  }
  .ring-gray-900\/10 {
    --tw-ring-color: color-mix(in srgb, oklch(21% 0.034 264.665) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-ring-color: color-mix(in oklab, var(--color-gray-900) 10%, transparent);
    }
  }
  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
    @media (forced-colors: active) {
      outline: 2px solid transparent;
      outline-offset: 2px;
    }
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .outline-1 {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .-outline-offset-1 {
    outline-offset: calc(1px * -1);
  }
  .outline-gray-300 {
    outline-color: var(--color-gray-300);
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-3xl {
    --tw-blur: blur(var(--blur-3xl));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-sm {
    --tw-blur: blur(var(--blur-sm));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-xs {
    --tw-blur: blur(var(--blur-xs));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .\!filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,) !important;
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-none {
    transition-property: none;
  }
  .duration-75 {
    --tw-duration: 75ms;
    transition-duration: 75ms;
  }
  .duration-100 {
    --tw-duration: 100ms;
    transition-duration: 100ms;
  }
  .duration-150 {
    --tw-duration: 150ms;
    transition-duration: 150ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .duration-700 {
    --tw-duration: 700ms;
    transition-duration: 700ms;
  }
  .duration-1000 {
    --tw-duration: 1000ms;
    transition-duration: 1000ms;
  }
  .ease-in {
    --tw-ease: var(--ease-in);
    transition-timing-function: var(--ease-in);
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .\[tool\:pytest\] {
    tool: pytest;
  }
  .ring-inset {
    --tw-ring-inset: inset;
  }
  .text-shadow-lg {
    text-shadow: 0px 1px 2px var(--tw-text-shadow-color, rgb(0 0 0 / 0.1)), 0px 3px 2px var(--tw-text-shadow-color, rgb(0 0 0 / 0.1)), 0px 4px 8px var(--tw-text-shadow-color, rgb(0 0 0 / 0.1));
  }
  .group-hover\:visible {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        visibility: visible;
      }
    }
  }
  .group-hover\:bg-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .group-hover\:text-gray-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-gray-500);
      }
    }
  }
  .group-hover\:text-ws-primary-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-ws-primary-600);
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:border-gray-400 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-400);
      }
    }
  }
  .hover\:border-transparent {
    &:hover {
      @media (hover: hover) {
        border-color: transparent;
      }
    }
  }
  .hover\:border-warning-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-warning-300);
      }
    }
  }
  .hover\:border-ws-primary-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-ws-primary-300);
      }
    }
  }
  .hover\:bg-blue-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-100);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-indigo-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-500);
      }
    }
  }
  .hover\:bg-purple-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-600);
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50);
      }
    }
  }
  .hover\:bg-red-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-100);
      }
    }
  }
  .hover\:bg-ws-primary-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-ws-primary-100);
      }
    }
  }
  .hover\:bg-ws-primary-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-ws-primary-800);
      }
    }
  }
  .hover\:text-blue-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .hover\:text-blue-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-900);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .hover\:text-red-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-800);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:text-ws-primary-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-ws-primary-700);
      }
    }
  }
  .hover\:text-ws-primary-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-ws-primary-800);
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:ring-gray-900\/20 {
    &:hover {
      @media (hover: hover) {
        --tw-ring-color: color-mix(in srgb, oklch(21% 0.034 264.665) 20%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-gray-900) 20%, transparent);
        }
      }
    }
  }
  .focus\:border-blue-500 {
    &:focus {
      border-color: var(--color-blue-500);
    }
  }
  .focus\:border-indigo-600 {
    &:focus {
      border-color: var(--color-indigo-600);
    }
  }
  .focus\:border-ws-primary-500 {
    &:focus {
      border-color: var(--color-ws-primary-500);
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-4 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:ring-gray-300 {
    &:focus {
      --tw-ring-color: var(--color-gray-300);
    }
  }
  .focus\:ring-indigo-500 {
    &:focus {
      --tw-ring-color: var(--color-indigo-500);
    }
  }
  .focus\:ring-ws-primary-500 {
    &:focus {
      --tw-ring-color: var(--color-ws-primary-500);
    }
  }
  .focus\:outline-hidden {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
  }
  .focus\:outline-2 {
    &:focus {
      outline-style: var(--tw-outline-style);
      outline-width: 2px;
    }
  }
  .focus\:-outline-offset-2 {
    &:focus {
      outline-offset: calc(2px * -1);
    }
  }
  .focus\:outline-indigo-600 {
    &:focus {
      outline-color: var(--color-indigo-600);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .focus\:hover\:bg-red-500 {
    &:focus {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-red-500);
        }
      }
    }
  }
  .hover\:focus\:bg-red-500 {
    &:hover {
      @media (hover: hover) {
        &:focus {
          background-color: var(--color-red-500);
        }
      }
    }
  }
  .focus-visible\:outline-2 {
    &:focus-visible {
      outline-style: var(--tw-outline-style);
      outline-width: 2px;
    }
  }
  .focus-visible\:outline-offset-2 {
    &:focus-visible {
      outline-offset: 2px;
    }
  }
  .focus-visible\:outline-indigo-600 {
    &:focus-visible {
      outline-color: var(--color-indigo-600);
    }
  }
  .focus-visible\:outline-ws-primary-600 {
    &:focus-visible {
      outline-color: var(--color-ws-primary-600);
    }
  }
  .active\:bg-purple-700 {
    &:active {
      background-color: var(--color-purple-700);
    }
  }
  .sm\:-top-80 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * -80);
    }
  }
  .sm\:top-\[calc\(100\%-30rem\)\] {
    @media (width >= 40rem) {
      top: calc(100% - 30rem);
    }
  }
  .sm\:left-\[calc\(50\%\+36rem\)\] {
    @media (width >= 40rem) {
      left: calc(50% + 36rem);
    }
  }
  .sm\:left-\[calc\(50\%-30rem\)\] {
    @media (width >= 40rem) {
      left: calc(50% - 30rem);
    }
  }
  .sm\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:mb-8 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }
  .sm\:block {
    @media (width >= 40rem) {
      display: block;
    }
  }
  .sm\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\:grid {
    @media (width >= 40rem) {
      display: grid;
    }
  }
  .sm\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\:inline {
    @media (width >= 40rem) {
      display: inline;
    }
  }
  .sm\:inline-block {
    @media (width >= 40rem) {
      display: inline-block;
    }
  }
  .sm\:inline-flex {
    @media (width >= 40rem) {
      display: inline-flex;
    }
  }
  .sm\:inline-grid {
    @media (width >= 40rem) {
      display: inline-grid;
    }
  }
  .sm\:size-4 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .sm\:w-\[72\.1875rem\] {
    @media (width >= 40rem) {
      width: 72.1875rem;
    }
  }
  .sm\:max-w-sm {
    @media (width >= 40rem) {
      max-width: var(--container-sm);
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\:grid-cols-3 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:justify-between {
    @media (width >= 40rem) {
      justify-content: space-between;
    }
  }
  .sm\:justify-center {
    @media (width >= 40rem) {
      justify-content: center;
    }
  }
  .sm\:space-x-5 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:divide-x {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-divide-x-reverse: 0;
        border-inline-style: var(--tw-border-style);
        border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
        border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
      }
    }
  }
  .sm\:divide-y-0 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-divide-y-reverse: 0;
        border-bottom-style: var(--tw-border-style);
        border-top-style: var(--tw-border-style);
        border-top-width: calc(0px * var(--tw-divide-y-reverse));
        border-bottom-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
      }
    }
  }
  .sm\:py-48 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 48);
    }
  }
  .sm\:pt-1 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 1);
    }
  }
  .sm\:pt-32 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 32);
    }
  }
  .sm\:text-left {
    @media (width >= 40rem) {
      text-align: left;
    }
  }
  .sm\:text-2xl {
    @media (width >= 40rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .sm\:text-7xl {
    @media (width >= 40rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
  .sm\:text-sm {
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .sm\:text-sm\/6 {
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: calc(var(--spacing) * 6);
    }
  }
  .sm\:text-xl\/8 {
    @media (width >= 40rem) {
      font-size: var(--text-xl);
      line-height: calc(var(--spacing) * 8);
    }
  }
  .sm\:leading-6 {
    @media (width >= 40rem) {
      --tw-leading: calc(var(--spacing) * 6);
      line-height: calc(var(--spacing) * 6);
    }
  }
  .sm\:ring-1 {
    @media (width >= 40rem) {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .sm\:ring-gray-900\/10 {
    @media (width >= 40rem) {
      --tw-ring-color: color-mix(in srgb, oklch(21% 0.034 264.665) 10%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-gray-900) 10%, transparent);
      }
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:grid {
    @media (width >= 48rem) {
      display: grid;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:inline {
    @media (width >= 48rem) {
      display: inline;
    }
  }
  .md\:inline-block {
    @media (width >= 48rem) {
      display: inline-block;
    }
  }
  .md\:inline-flex {
    @media (width >= 48rem) {
      display: inline-flex;
    }
  }
  .md\:inline-grid {
    @media (width >= 48rem) {
      display: inline-grid;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:grid {
    @media (width >= 64rem) {
      display: grid;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:inline {
    @media (width >= 64rem) {
      display: inline;
    }
  }
  .lg\:inline-block {
    @media (width >= 64rem) {
      display: inline-block;
    }
  }
  .lg\:inline-flex {
    @media (width >= 64rem) {
      display: inline-flex;
    }
  }
  .lg\:inline-grid {
    @media (width >= 64rem) {
      display: inline-grid;
    }
  }
  .lg\:flex-1 {
    @media (width >= 64rem) {
      flex: 1;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:justify-end {
    @media (width >= 64rem) {
      justify-content: flex-end;
    }
  }
  .lg\:gap-x-12 {
    @media (width >= 64rem) {
      column-gap: calc(var(--spacing) * 12);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:py-56 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 56);
    }
  }
  .\32 xl\:block {
    @media (width >= 70rem) {
      display: block;
    }
  }
  .\32 xl\:flex {
    @media (width >= 70rem) {
      display: flex;
    }
  }
  .\32 xl\:grid {
    @media (width >= 70rem) {
      display: grid;
    }
  }
  .\32 xl\:hidden {
    @media (width >= 70rem) {
      display: none;
    }
  }
  .\32 xl\:inline {
    @media (width >= 70rem) {
      display: inline;
    }
  }
  .\32 xl\:inline-block {
    @media (width >= 70rem) {
      display: inline-block;
    }
  }
  .\32 xl\:inline-flex {
    @media (width >= 70rem) {
      display: inline-flex;
    }
  }
  .\32 xl\:inline-grid {
    @media (width >= 70rem) {
      display: inline-grid;
    }
  }
  .xl\:-mb-8 {
    @media (width >= 80rem) {
      margin-bottom: calc(var(--spacing) * -8);
    }
  }
  .xl\:block {
    @media (width >= 80rem) {
      display: block;
    }
  }
  .xl\:flex {
    @media (width >= 80rem) {
      display: flex;
    }
  }
  .xl\:grid {
    @media (width >= 80rem) {
      display: grid;
    }
  }
  .xl\:hidden {
    @media (width >= 80rem) {
      display: none;
    }
  }
  .xl\:inline {
    @media (width >= 80rem) {
      display: inline;
    }
  }
  .xl\:inline-block {
    @media (width >= 80rem) {
      display: inline-block;
    }
  }
  .xl\:inline-flex {
    @media (width >= 80rem) {
      display: inline-flex;
    }
  }
  .xl\:inline-grid {
    @media (width >= 80rem) {
      display: inline-grid;
    }
  }
  .xl\:w-96 {
    @media (width >= 80rem) {
      width: calc(var(--spacing) * 96);
    }
  }
  .xl\:flex-none {
    @media (width >= 80rem) {
      flex: none;
    }
  }
  .xl\:pb-32 {
    @media (width >= 80rem) {
      padding-bottom: calc(var(--spacing) * 32);
    }
  }
}
@layer theme {
  :root {
    --color-purple-50: #faf5ff;
    --color-purple-100: #f3e8ff;
    --color-purple-200: #e9d5ff;
    --color-purple-300: #d8b4fe;
    --color-purple-400: #c084fc;
    --color-purple-500: #a855f7;
    --color-purple-600: #9333ea;
    --color-purple-700: #7c3aed;
    --color-purple-800: #6b21a8;
    --color-purple-900: #581c87;
    --color-red-50: #fef2f2;
    --color-red-100: #fee2e2;
    --color-red-200: #fecaca;
    --color-red-300: #fca5a5;
    --color-red-400: #f87171;
    --color-red-500: #ef4444;
    --color-red-600: #dc2626;
    --color-red-700: #b91c1c;
    --color-red-800: #991b1b;
    --color-red-900: #7f1d1d;
    --color-blue-50: #eff6ff;
    --color-blue-100: #dbeafe;
    --color-blue-200: #bfdbfe;
    --color-blue-300: #93c5fd;
    --color-blue-400: #60a5fa;
    --color-blue-600: #2563eb;
    --color-blue-700: #1d4ed8;
    --color-blue-800: #1e40af;
    --color-blue-900: #1e3a8a;
    --color-green-50: #f0fdf4;
    --color-green-100: #dcfce7;
    --color-green-200: #bbf7d0;
    --color-green-300: #86efac;
    --color-green-400: #4ade80;
    --color-green-500: #22c55e;
    --color-green-600: #16a34a;
    --color-green-700: #15803d;
    --color-green-800: #166534;
    --color-green-900: #14532d;
    --color-yellow-50: #fefce8;
    --color-yellow-100: #fef3c7;
    --color-yellow-200: #fde68a;
    --color-yellow-300: #fcd34d;
    --color-yellow-400: #fbbf24;
    --color-yellow-500: #f59e0b;
    --color-yellow-600: #d97706;
    --color-yellow-700: #b45309;
    --color-yellow-800: #92400e;
    --color-yellow-900: #78350f;
    --color-indigo-50: #eef2ff;
    --color-indigo-100: #e0e7ff;
    --color-indigo-200: #c7d2fe;
    --color-indigo-300: #a5b4fc;
    --color-indigo-400: #818cf8;
    --color-indigo-500: #6366f1;
    --color-indigo-600: #4f46e5;
    --color-indigo-700: #4338ca;
    --color-indigo-800: #3730a3;
    --color-indigo-900: #312e81;
    --color-pink-50: #fdf2f8;
    --color-pink-100: #fce7f3;
    --color-pink-200: #fbcfe8;
    --color-pink-300: #f9a8d4;
    --color-pink-400: #f472b6;
    --color-pink-500: #ec4899;
    --color-pink-600: #db2777;
    --color-pink-700: #be185d;
    --color-pink-800: #9d174d;
    --color-pink-900: #831843;
  }
}
@layer components {
  .container {
    margin-inline: auto !important;
    padding-inline: 1rem !important;
  }
  @media (width >= 40rem) {
    .container {
      padding-inline: 1.5rem !important;
    }
  }
  @media (width >= 64rem) {
    .container {
      padding-inline: 2rem !important;
    }
  }
  .btn-primary {
    background-color: var(--color-ws-primary-700);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
    border: none;
    cursor: pointer;
  }
  .btn-primary:hover {
    background-color: var(--color-ws-primary-800);
  }
  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-ws-primary-500);
  }
  .btn-secondary {
    background-color: var(--color-ws-secondary-100);
    color: var(--color-ws-secondary-900);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
    border: none;
    cursor: pointer;
  }
  .btn-secondary:hover {
    background-color: var(--color-ws-secondary-200);
  }
  .btn-success {
    cursor: pointer;
    border-radius: var(--radius-md);
    --tw-border-style: none;
    border-style: none;
    background-color: var(--color-success-600);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-white);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .btn-success:hover {
    background-color: var(--color-success-700);
  }
  .btn-warning {
    cursor: pointer;
    border-radius: var(--radius-md);
    --tw-border-style: none;
    border-style: none;
    background-color: var(--color-warning-500);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-white);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .btn-warning:hover {
    background-color: var(--color-warning-600);
  }
  .btn-danger {
    cursor: pointer;
    border-radius: var(--radius-md);
    --tw-border-style: none;
    border-style: none;
    background-color: var(--color-danger-600);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-white);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .btn-danger:hover {
    background-color: var(--color-danger-700);
  }
  .form-input {
    display: block;
    width: 100%;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-ws-secondary-300);
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .form-input:focus {
    border-color: var(--color-ws-primary-500);
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-ring-color: var(--color-ws-primary-500);
    --tw-outline-style: none;
    outline-style: none;
  }
  .form-input::placeholder {
    color: var(--color-ws-secondary-400);
  }
  .form-select {
    display: block;
    width: 100%;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-ws-secondary-300);
    background-color: var(--color-white);
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .form-select:focus {
    border-color: var(--color-ws-primary-500);
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-ring-color: var(--color-ws-primary-500);
    --tw-outline-style: none;
    outline-style: none;
  }
  .form-textarea {
    display: block;
    width: 100%;
    resize: vertical;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-ws-secondary-300);
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .form-textarea:focus {
    border-color: var(--color-ws-primary-500);
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-ring-color: var(--color-ws-primary-500);
    --tw-outline-style: none;
    outline-style: none;
  }
  .form-textarea::placeholder {
    color: var(--color-ws-secondary-400);
  }
  .form-label {
    margin-bottom: calc(var(--spacing) * 1);
    display: block;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-ws-secondary-700);
  }
  .form-error {
    margin-top: calc(var(--spacing) * 1);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--color-danger-600);
  }
  .form-help {
    margin-top: calc(var(--spacing) * 1);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--color-ws-secondary-500);
  }
  .card {
    border-radius: var(--radius-lg);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-ws-secondary-200);
    background-color: var(--color-white);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .card-header {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--color-ws-secondary-200);
    padding: calc(var(--spacing) * 6);
  }
  .card-body {
    padding: calc(var(--spacing) * 6);
  }
  .card-footer {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
    border-color: var(--color-ws-secondary-200);
    background-color: var(--color-ws-secondary-50);
    padding: calc(var(--spacing) * 6);
  }
  .alert {
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    padding: calc(var(--spacing) * 4);
  }
  .alert-success {
    border-color: var(--color-success-200);
    background-color: var(--color-success-50);
    color: var(--color-success-800);
  }
  .alert-warning {
    border-color: var(--color-warning-200);
    background-color: var(--color-warning-50);
    color: var(--color-warning-800);
  }
  .alert-danger {
    border-color: var(--color-danger-200);
    background-color: var(--color-danger-50);
    color: var(--color-danger-800);
  }
  .alert-info {
    border-color: var(--color-ws-secondary-200);
    background-color: var(--color-ws-secondary-50);
    color: var(--color-ws-secondary-800);
  }
  .nav-link {
    border-radius: var(--radius-md);
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-ws-secondary-600);
    text-decoration-line: none;
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .nav-link:hover {
    color: var(--color-ws-primary-700);
  }
  .nav-link-active {
    background-color: var(--color-ws-primary-50);
    color: var(--color-ws-primary-700);
  }
  .widget-card {
    border-radius: var(--radius-lg);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-ws-secondary-200);
    background-color: var(--color-white);
    padding: calc(var(--spacing) * 6);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .widget-card:hover {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .widget-icon {
    display: flex;
    height: calc(var(--spacing) * 12);
    width: calc(var(--spacing) * 12);
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-lg);
    background-color: var(--color-ws-primary-100);
  }
  .widget-title {
    margin-bottom: calc(var(--spacing) * 2);
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    color: var(--color-ws-secondary-900);
  }
  .widget-description {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--color-ws-secondary-600);
  }
  .title-divider {
    margin-bottom: calc(var(--spacing) * 6);
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
    border-color: var(--color-ws-primary-700);
    padding-bottom: calc(var(--spacing) * 2);
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    color: var(--color-ws-secondary-900);
  }
  .title-divider span {
    background-color: var(--color-white);
    padding-right: calc(var(--spacing) * 4);
  }
  .title-divider small {
    margin-top: calc(var(--spacing) * 1);
    display: block;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
    color: var(--color-ws-secondary-600);
  }
}
@layer utilities {
  .text-brand {
    color: var(--color-ws-primary-700);
  }
  .bg-brand {
    background-color: var(--color-ws-primary-700);
  }
  .border-brand {
    border-color: var(--color-ws-primary-700);
  }
  .shadow-brand {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(190 62 29 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(190 62 29 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
@layer base {
  .row {
    margin-inline: calc(var(--spacing) * -4);
    display: flex;
    flex-wrap: wrap;
  }
  .col-md-1 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-2 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-3 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-4 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-5 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-6 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-7 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-8 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-9 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-10 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-11 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  .col-md-12 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  @media (min-width: 768px) {
    .col-md-1 {
      width: calc(1/12 * 100%);
    }
    .col-md-2 {
      width: calc(2/12 * 100%);
    }
    .col-md-3 {
      width: calc(3/12 * 100%);
    }
    .col-md-4 {
      width: calc(4/12 * 100%);
    }
    .col-md-5 {
      width: calc(5/12 * 100%);
    }
    .col-md-6 {
      width: calc(6/12 * 100%);
    }
    .col-md-7 {
      width: calc(7/12 * 100%);
    }
    .col-md-8 {
      width: calc(8/12 * 100%);
    }
    .col-md-9 {
      width: calc(9/12 * 100%);
    }
    .col-md-10 {
      width: calc(10/12 * 100%);
    }
    .col-md-11 {
      width: calc(11/12 * 100%);
    }
    .col-md-12 {
      width: 100%;
    }
  }
  .col-sm-6 {
    width: 100%;
    padding-inline: calc(var(--spacing) * 4);
  }
  @media (min-width: 640px) {
    .col-sm-6 {
      width: calc(6/12 * 100%);
    }
  }
  .form-control {
    display: block;
    width: 100%;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-ws-secondary-300);
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .form-control:focus {
    border-color: var(--color-ws-primary-500);
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-ring-color: var(--color-ws-primary-500);
    --tw-outline-style: none;
    outline-style: none;
  }
  .form-group {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .control-label {
    margin-bottom: calc(var(--spacing) * 1);
    display: block;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-ws-secondary-700);
  }
  .has-error .form-control {
    border-color: var(--color-danger-500);
  }
  .has-error .form-control:focus {
    border-color: var(--color-danger-500);
    --tw-ring-color: var(--color-danger-500);
  }
  .text-danger {
    margin-top: calc(var(--spacing) * 1);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--color-danger-600);
  }
  .text-info {
    margin-top: calc(var(--spacing) * 1);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--color-ws-secondary-500);
  }
  .text-success {
    color: var(--color-success-600);
  }
  .text-warning {
    color: var(--color-warning-600);
  }
  .btn {
    display: inline-flex;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: transparent;
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    text-decoration-line: none;
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .btn:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-ring-color: var(--color-blue-500);
    --tw-outline-style: none;
    outline-style: none;
  }
  .btn-default {
    background-color: var(--color-ws-secondary-100);
    color: var(--color-ws-secondary-900);
  }
  .btn-default:hover {
    background-color: var(--color-ws-secondary-200);
  }
  .mb20 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb40 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb80 {
    margin-bottom: calc(var(--spacing) * 20);
  }
  .mt10 {
    margin-top: calc(var(--spacing) * 2.5);
  }
  .mt20 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt40 {
    margin-top: calc(var(--spacing) * 10);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .hide {
    display: none;
  }
  .show {
    display: block;
  }
  .pull-left {
    float: left;
  }
  .pull-right {
    float: right;
  }
}
@layer utilities {
  .space-y-0\.5 > * + * {
    margin-top: 0.125rem;
  }
  .space-y-1\.5 > * + * {
    margin-top: 0.375rem;
  }
  .space-y-2\.5 > * + * {
    margin-top: 0.625rem;
  }
  .space-x-0\.5 > * + * {
    margin-left: 0.125rem;
  }
  .space-x-1\.5 > * + * {
    margin-left: 0.375rem;
  }
  .space-x-2\.5 > * + * {
    margin-left: 0.625rem;
  }
  .p-0\.5 {
    padding: 0.125rem;
  }
  .px-0\.5 {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
  }
  .py-0\.5 {
    padding-top: 0.125rem;
    padding-bottom: 0.125rem;
  }
  .pt-0\.5 {
    padding-top: 0.125rem;
  }
  .pr-0\.5 {
    padding-right: 0.125rem;
  }
  .pb-0\.5 {
    padding-bottom: 0.125rem;
  }
  .pl-0\.5 {
    padding-left: 0.125rem;
  }
  .m-0\.5 {
    margin: 0.125rem;
  }
  .mx-0\.5 {
    margin-left: 0.125rem;
    margin-right: 0.125rem;
  }
  .my-0\.5 {
    margin-top: 0.125rem;
    margin-bottom: 0.125rem;
  }
  .mt-0\.5 {
    margin-top: 0.125rem;
  }
  .mr-0\.5 {
    margin-right: 0.125rem;
  }
  .mb-0\.5 {
    margin-bottom: 0.125rem;
  }
  .ml-0\.5 {
    margin-left: 0.125rem;
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-text-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-text-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-text-shadow-color: initial;
      --tw-text-shadow-alpha: 100%;
    }
  }
}
