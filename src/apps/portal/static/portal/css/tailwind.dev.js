/** @type {import('tailwindcss').Config} */
export default {
  content: [
    // Development: Include ALL possible content to force generation
    "../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}",
    "../../../templates/**/*.{html,js,ts,jsx,tsx,vue,php,md}",
    "../js/**/*.{js,ts}",
    "../../../**/*.{html,js,ts,jsx,tsx,vue}",
    
    // Force generation by including a wide pattern
    "./**/*.{html,js,ts,jsx,tsx,vue,php,twig,erb,blade}",
  ],

  // Disable automatic container generation (using custom container in tailwind.css)
  corePlugins: {
    container: false,
  },

  // Add missing colors for TailwindCSS v4.1
  theme: {
    extend: {
      colors: {
        purple: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        red: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        blue: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        green: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        yellow: {
          50: '#fefce8',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
        },
        pink: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        }
      }
    }
  },

  // Minimal safelist - let JIT do the work for development
  safelist: [
    // Only dynamic patterns that cannot be detected statically
    { pattern: /^grid-cols-\d+$/ },
    { pattern: /^w-\[.*\]$/ },
    { pattern: /^h-\[.*\]$/ },

    // Finder-v2 widget specific utilities (for success notifications and layout)
    'size-5', 'size-20', 'shrink-0', 'sr-only', 'shadow-xs',
    'ring-1', 'ring-inset', 'ring-gray-300',
    'divide-y', 'divide-x', 'divide-gray-200', 'sm:divide-x', 'sm:divide-y-0',
    'sm:grid-cols-3', 'sm:items-center', 'sm:justify-between', 'sm:space-x-5', 'sm:pt-1', 'sm:mt-0', 'sm:text-left', 'sm:text-2xl',
    'bg-green-50', 'text-green-400', 'text-green-700', 'text-green-800', 'border-green-200',
    'bg-red-50', 'text-red-400', 'text-red-700', 'text-red-800', 'border-red-200',
    'bg-yellow-50', 'text-yellow-400', 'text-yellow-700', 'text-yellow-800', 'border-yellow-200',
    'bg-blue-50', 'text-blue-400', 'text-blue-700', 'text-blue-800', 'border-blue-200',
    'mb-6', 'ml-3', 'mt-2', 'rounded-md', 'font-medium',

    // Brand colors (from original config) - these are custom and may not be detected
    'bg-ws-primary-50', 'bg-ws-primary-100', 'bg-ws-primary-200', 'bg-ws-primary-300', 'bg-ws-primary-400', 'bg-ws-primary-500', 'bg-ws-primary-600', 'bg-ws-primary-700', 'bg-ws-primary-800', 'bg-ws-primary-900',
    'text-ws-primary-50', 'text-ws-primary-100', 'text-ws-primary-200', 'text-ws-primary-300', 'text-ws-primary-400', 'text-ws-primary-500', 'text-ws-primary-600', 'text-ws-primary-700', 'text-ws-primary-800', 'text-ws-primary-900',
    'border-ws-primary-50', 'border-ws-primary-100', 'border-ws-primary-200', 'border-ws-primary-300', 'border-ws-primary-400', 'border-ws-primary-500', 'border-ws-primary-600', 'border-ws-primary-700', 'border-ws-primary-800', 'border-ws-primary-900',
    
    'bg-ws-secondary-50', 'bg-ws-secondary-100', 'bg-ws-secondary-200', 'bg-ws-secondary-300', 'bg-ws-secondary-400', 'bg-ws-secondary-500', 'bg-ws-secondary-600', 'bg-ws-secondary-700', 'bg-ws-secondary-800', 'bg-ws-secondary-900',
    'text-ws-secondary-50', 'text-ws-secondary-100', 'text-ws-secondary-200', 'text-ws-secondary-300', 'text-ws-secondary-400', 'text-ws-secondary-500', 'text-ws-secondary-600', 'text-ws-secondary-700', 'text-ws-secondary-800', 'text-ws-secondary-900',
    'border-ws-secondary-50', 'border-ws-secondary-100', 'border-ws-secondary-200', 'border-ws-secondary-300', 'border-ws-secondary-400', 'border-ws-secondary-500', 'border-ws-secondary-600', 'border-ws-secondary-700', 'border-ws-secondary-800', 'border-ws-secondary-900',
    
    // State colors
    'bg-success-50', 'bg-success-100', 'bg-success-200', 'bg-success-300', 'bg-success-400', 'bg-success-500', 'bg-success-600', 'bg-success-700', 'bg-success-800', 'bg-success-900',
    'text-success-50', 'text-success-100', 'text-success-200', 'text-success-300', 'text-success-400', 'text-success-500', 'text-success-600', 'text-success-700', 'text-success-800', 'text-success-900',
    'border-success-50', 'border-success-100', 'border-success-200', 'border-success-300', 'border-success-400', 'border-success-500', 'border-success-600', 'border-success-700', 'border-success-800', 'border-success-900',
    
    'bg-warning-50', 'bg-warning-100', 'bg-warning-200', 'bg-warning-300', 'bg-warning-400', 'bg-warning-500', 'bg-warning-600', 'bg-warning-700', 'bg-warning-800', 'bg-warning-900',
    'text-warning-50', 'text-warning-100', 'text-warning-200', 'text-warning-300', 'text-warning-400', 'text-warning-500', 'text-warning-600', 'text-warning-700', 'text-warning-800', 'text-warning-900',
    'border-warning-50', 'border-warning-100', 'border-warning-200', 'border-warning-300', 'border-warning-400', 'border-warning-500', 'border-warning-600', 'border-warning-700', 'border-warning-800', 'border-warning-900',
    
    'bg-danger-50', 'bg-danger-100', 'bg-danger-200', 'bg-danger-300', 'bg-danger-400', 'bg-danger-500', 'bg-danger-600', 'bg-danger-700', 'bg-danger-800', 'bg-danger-900',
    'text-danger-50', 'text-danger-100', 'text-danger-200', 'text-danger-300', 'text-danger-400', 'text-danger-500', 'text-danger-600', 'text-danger-700', 'text-danger-800', 'text-danger-900',
    'border-danger-50', 'border-danger-100', 'border-danger-200', 'border-danger-300', 'border-danger-400', 'border-danger-500', 'border-danger-600', 'border-danger-700', 'border-danger-800', 'border-danger-900',
  ],

  // Enable dark mode
  darkMode: 'class'
} 