@import "tailwindcss";

/* TailwindCSS v4 Theme Configuration */
@theme {
  --color-ws-primary-50: #f0f9ff;
  --color-ws-primary-100: #e0f2fe;
  --color-ws-primary-200: #bae6fd;
  --color-ws-primary-300: #7dd3fc;
  --color-ws-primary-400: #38bdf8;
  --color-ws-primary-500: #0ea5e9;
  --color-ws-primary-600: #0284c7;
  --color-ws-primary-700: #0369a1;
  --color-ws-primary-800: #075985;
  --color-ws-primary-900: #0c4a6e;

  --color-ws-secondary-50: #f8fafc;
  --color-ws-secondary-100: #f1f5f9;
  --color-ws-secondary-200: #e2e8f0;
  --color-ws-secondary-300: #cbd5e1;
  --color-ws-secondary-400: #94a3b8;
  --color-ws-secondary-500: #64748b;
  --color-ws-secondary-600: #475569;
  --color-ws-secondary-700: #334155;
  --color-ws-secondary-800: #1e293b;
  --color-ws-secondary-900: #0f172a;

  --color-indigo-200: #c7d2fe;
  --color-indigo-500: #6366f1;
  --color-indigo-600: #4f46e5;

  --font-family-sans: 'Inter', system-ui, sans-serif;
  --max-width-widget: 600px;
  --min-width-widget: 250px;
}

/* Finder-v2 Widget Styles */

/* Base widget container */
.widget {
  font-family: var(--font-family-sans);
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  max-width: var(--max-width-widget);
  min-width: var(--min-width-widget);
}

/* Widget tabs styling removed - finder-v2 only supports single search type */

/* External spinner animation for CustomSelector loading states */
.spinner-external svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Form styling */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.form-control:focus {
  outline: none;
  border-color: var(--color-ws-primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-control:disabled {
  background-color: #f3f4f6;
  color: #6b7280;
  cursor: not-allowed;
}

/* Button styling */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  color: white;
  background-color: var(--color-ws-primary-600);
}

.btn-primary:hover {
  background-color: var(--color-ws-primary-700);
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.3);
}

.btn-secondary {
  color: #374151;
  background-color: white;
  border-color: #d1d5db;
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

/* Loading states */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
}

.spinner {
  animation: spin 1s linear infinite;
  height: 1.25rem;
  width: 1.25rem;
  color: var(--color-ws-primary-600);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Results table */
.results-table {
  width: 100%;
  border-collapse: collapse;
}

.results-table th {
  padding: 0.5rem 1rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.results-table td {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
}

.results-table tr.oem {
  background-color: #fefce8;
}

.results-table tr.oem td {
  color: #92400e;
}

/* Responsive design */
@media (max-width: 640px) {
  .widget {
    font-size: 13px;
  }

  .form-control {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .results-table th,
  .results-table td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
}

/* Widget-specific animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Error states */
.error {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.error-border {
  border-color: #fca5a5;
}

.error-border:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}
