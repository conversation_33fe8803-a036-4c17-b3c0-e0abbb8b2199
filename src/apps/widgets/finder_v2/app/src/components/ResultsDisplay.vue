<template>
  <div class="results-display">
    <div v-if="loading" class="loading">
      <svg class="spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-2">Loading results...</span>
    </div>

    <div v-else-if="results.length === 0" class="no-results">
      <p class="no-results-text">No results found. Please try different search criteria.</p>
    </div>

    <div v-else class="results-content">
      <div class="results-header">
        <h5 class="results-title">
          Search Results ({{ results.length }} {{ results.length === 1 ? 'result' : 'results' }})
        </h5>
        <p class="results-note">
          Please note: <span class="oem-highlight">highlighted</span> items are for OEM wheels
        </p>
      </div>

      <div class="table-container">
        <table class="results-table">
          <thead>
            <tr>
              <th>Vehicle</th>
              <th>Year</th>
              <th>Trim</th>
              <th>Tire</th>
              <th>Rim</th>
              <th>THD</th>
              <th>CB</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(result, index) in results"
              :key="index"
              :class="{ 'oem': result.is_oem }"
            >
              <td class="font-medium">
                {{ result.make }} {{ result.model }}
              </td>
              <td>{{ result.year }}</td>
              <td>{{ result.trim || '-' }}</td>
              <td>
                <span v-if="result.tire">
                  {{ result.tire.width }}/{{ result.tire.aspect_ratio }}R{{ result.tire.diameter }}
                </span>
                <span v-else>-</span>
              </td>
              <td>
                <span v-if="result.rim">
                  {{ result.rim.diameter }}x{{ result.rim.width }}
                </span>
                <span v-else>-</span>
              </td>
              <td>
                <span v-if="result.rim && result.rim.bolt_pattern">
                  {{ result.rim.bolt_pattern }}
                </span>
                <span v-else>-</span>
              </td>
              <td>
                <span v-if="result.rim && result.rim.center_bore">
                  {{ result.rim.center_bore }}
                </span>
                <span v-else>-</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Show "See on Wheel-Size.com" button if configured -->
      <div v-if="showWheelSizeButton" class="wheel-size-button-container">
        <a
          :href="wheelSizeUrl"
          target="_blank"
          rel="noopener noreferrer"
          class="btn btn-secondary"
        >
          See on Wheel-Size.com
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/finder'

export default {
  name: 'ResultsDisplay',
  setup() {
    const finderStore = useFinderStore()

    const { loading, results, config } = storeToRefs(finderStore)

    const showWheelSizeButton = computed(() => {
      return !config.value.widgetConfig?.blocks?.button_to_ws?.hide
    })

    const wheelSizeUrl = computed(() => {
      const utm = config.value.widgetConfig?.utm || ''
      return `https://www.wheel-size.com${utm}`
    })

    return {
      loading,
      results,
      showWheelSizeButton,
      wheelSizeUrl
    }
  }
}
</script>

<style scoped>
.results-display {
  width: 100%;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  color: #6b7280;
}

.spinner {
  animation: spin 1s linear infinite;
  height: 1.25rem;
  width: 1.25rem;
  color: var(--color-ws-primary-600);
}

.no-results {
  padding: 2rem 0;
}

.no-results-text {
  color: #6b7280;
  text-align: center;
}

.results-content {
  width: 100%;
}

.results-header {
  margin-bottom: 1rem;
}

.results-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
}

.results-note {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.oem-highlight {
  font-weight: 500;
  color: #d97706;
}

.table-container {
  overflow-x: auto;
}

.wheel-size-button-container {
  margin-top: 1.5rem;
  text-align: center;
}
</style>
