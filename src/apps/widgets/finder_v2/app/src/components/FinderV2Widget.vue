<template>
  <div class="finder-v2-widget p-1" data-iframe-height>
    <!-- Vehicle Search (finder-v2 only supports by_vehicle search type) -->
    <div class="search-content">
      <VehicleSearch />
    </div>

    <!-- Results Display -->
    <div v-if="hasResults" class="results-section">
      <ResultsDisplay />
    </div>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { useFinderStore } from '../stores/finder'
import VehicleSearch from './VehicleSearch.vue'
import ResultsDisplay from './ResultsDisplay.vue'

export default {
  name: 'FinderV2Widget',
  components: {
    VehicleSearch,
    ResultsDisplay
  },
  setup() {
    const finderStore = useFinderStore()

    // Widget configuration from global config
    const config = window.FinderV2Config || {}

    // Check if there are search results
    const hasResults = computed(() => {
      return finderStore.results.length > 0
    })

    onMounted(() => {
      // Initialize finder store with configuration
      finderStore.initialize(config)
    })

    return {
      hasResults
    }
  }
}
</script>

<style scoped>
.finder-v2-widget {
  width: 100%;
}

.search-content {
  padding: 1rem 0;
}

.results-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}
</style>
