<template>
  <div class="vehicle-search">
    <h4 class="search-title">
      Specify make, year and model to find matching wheels:
    </h4>

    <form @submit.prevent="handleSearch" class="search-form">
      <!-- Primary Flow: Year → Make → Model → Modifications -->
      <div v-if="flowType === 'primary'" class="form-grid">
        <!-- Year Selection -->
        <div class="form-group">
          <label class="form-label">Year</label>
          <CustomSelector
            v-model="selectedYear"
            :options="years"
            :loading="loading && !years.length"
            :preloader="loadingYears"
            :state-loaded="stateLoadedYears"
            :auto-expand="false"
            placeholder="Select Year"
            @change="onYearChange"
          />
        </div>

        <!-- Make Selection -->
        <div class="form-group">
          <label class="form-label">Make</label>
          <CustomSelector
            v-model="selectedMake"
            :options="makes"
            :loading="loading && selectedYear && !makes.length"
            :preloader="loadingMakes"
            :state-loaded="stateLoadedMakes"
            :disabled="!selectedYear"
            placeholder="Select Make"
            @change="onMakeChange"
          />
        </div>

        <!-- Model Selection -->
        <div class="form-group">
          <label class="form-label">Model</label>
          <CustomSelector
            v-model="selectedModel"
            :options="models"
            :loading="loading && selectedMake && !models.length"
            :preloader="loadingModels"
            :state-loaded="stateLoadedModels"
            :disabled="!selectedMake"
            placeholder="Select Model"
            @change="onModelChange"
          />
        </div>

        <!-- Modification Selection -->
        <div class="form-group">
          <label class="form-label">Modification</label>
          <CustomSelector
            v-model="selectedModification"
            :options="modifications"
            :loading="loading && selectedModel && !modifications.length"
            :preloader="loadingModifications"
            :state-loaded="stateLoadedModifications"
            :disabled="!selectedModel"
            placeholder="Select Modification"
          />
        </div>
      </div>

      <!-- Alternative Flow: Make → Model → Generation → Modifications -->
      <div v-else class="form-grid">
        <!-- Make Selection -->
        <div class="form-group">
          <label class="form-label">Make</label>
          <CustomSelector
            v-model="selectedMake"
            :options="makes"
            :loading="loading && !makes.length"
            :preloader="loadingMakes"
            :state-loaded="stateLoadedMakes"
            :auto-expand="false"
            placeholder="Select Make"
            @change="onMakeChange"
          />
        </div>

        <!-- Model Selection -->
        <div class="form-group">
          <label class="form-label">Model</label>
          <CustomSelector
            v-model="selectedModel"
            :options="models"
            :loading="loading && selectedMake && !models.length"
            :preloader="loadingModels"
            :state-loaded="stateLoadedModels"
            :disabled="!selectedMake"
            placeholder="Select Model"
            @change="onModelChange"
          />
        </div>

        <!-- Generation Selection -->
        <div class="form-group">
          <label class="form-label">Generation</label>
          <CustomSelector
            v-model="selectedGeneration"
            :options="generations"
            :loading="loading && selectedModel && !generations.length"
            :preloader="loadingGenerations"
            :state-loaded="stateLoadedGenerations"
            :disabled="!selectedModel"
            placeholder="Select Generation"
            @change="onGenerationChange"
          />
        </div>

        <!-- Modification Selection -->
        <div class="form-group">
          <label class="form-label">Modification</label>
          <CustomSelector
            v-model="selectedModification"
            :options="modifications"
            :loading="loading && selectedGeneration && !modifications.length"
            :preloader="loadingModifications"
            :state-loaded="stateLoadedModifications"
            :disabled="!selectedGeneration"
            placeholder="Select Modification"
          />
        </div>
      </div>

      <!-- Search Button removed: automatic search enabled -->
    </form>

    <!-- Error Display -->
    <div v-if="error" class="error-container">
      <p class="error">{{ error }}</p>
    </div>
  </div>
</template>

<script>
import { computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/finder'
import CustomSelector from './CustomSelector.vue'

export default {
  name: 'VehicleSearch',
  components: {
    CustomSelector
  },
  setup() {
    const finderStore = useFinderStore()

    const {
      loading,
      error,
      selectedYear,
      selectedMake,
      selectedModel,
      selectedModification,
      selectedGeneration,
      years,
      makes,
      models,
      modifications,
      generations,
      flowType,
      // Individual loading states
      loadingYears,
      loadingMakes,
      loadingModels,
      loadingGenerations,
      loadingModifications,
      // State loaded flags
      stateLoadedYears,
      stateLoadedMakes,
      stateLoadedModels,
      stateLoadedGenerations,
      stateLoadedModifications
    } = storeToRefs(finderStore)

    const canSearch = computed(() => {
      if (flowType.value === 'primary') {
        return selectedYear.value && selectedMake.value && selectedModel.value && selectedModification.value
      } else {
        return selectedMake.value && selectedModel.value && selectedGeneration.value && selectedModification.value
      }
    })

    // Automatically trigger search when all selections are ready
    watch(canSearch, async (ready, wasReady) => {
      if (ready && !wasReady) {
        await handleSearch()
      }
    })

    // Trigger a new search whenever the user changes the Modification selector.
    // This handles the scenario where canSearch was already true (second/third change).
    watch(selectedModification, async (newVal, oldVal) => {
      if (newVal && newVal !== oldVal && !loading.value) {
        await handleSearch()
      }
    })

    async function onYearChange() {
      finderStore.selectedMake = ''
      finderStore.selectedModel = ''
      finderStore.selectedModification = ''
      finderStore.models = []
      finderStore.modifications = []

      if (selectedYear.value) {
        await finderStore.loadMakes(selectedYear.value)
      }
    }

    async function onMakeChange() {
      finderStore.selectedModel = ''
      finderStore.selectedModification = ''
      finderStore.selectedGeneration = ''
      finderStore.models = []
      finderStore.modifications = []
      finderStore.generations = []

      if (selectedMake.value) {
        if (flowType.value === 'primary') {
          await finderStore.loadModels(selectedMake.value, selectedYear.value)
        } else {
          await finderStore.loadModels(selectedMake.value)
        }
      }
    }

    async function onModelChange() {
      finderStore.selectedModification = ''
      finderStore.selectedGeneration = ''
      finderStore.modifications = []
      finderStore.generations = []

      if (selectedModel.value) {
        if (flowType.value === 'primary') {
          await finderStore.loadModifications(selectedMake.value, selectedModel.value, selectedYear.value)
        } else {
          await finderStore.loadGenerations(selectedMake.value, selectedModel.value)
        }
      }
    }

    async function onGenerationChange() {
      finderStore.selectedModification = ''
      finderStore.modifications = []

      if (selectedGeneration.value) {
        await finderStore.loadModifications(selectedMake.value, selectedModel.value, selectedGeneration.value)
      }
    }

    async function handleSearch() {
      if (canSearch.value) {
        await finderStore.searchByVehicle()
      }
    }

    return {
      loading,
      error,
      selectedYear,
      selectedMake,
      selectedModel,
      selectedModification,
      selectedGeneration,
      years,
      makes,
      models,
      modifications,
      generations,
      flowType,
      // Individual loading states
      loadingYears,
      loadingMakes,
      loadingModels,
      loadingGenerations,
      loadingModifications,
      // State loaded flags
      stateLoadedYears,
      stateLoadedMakes,
      stateLoadedModels,
      stateLoadedGenerations,
      stateLoadedModifications,
      canSearch,
      onYearChange,
      onMakeChange,
      onModelChange,
      onGenerationChange,
      handleSearch
    }
  }
}
</script>

<style scoped>
.vehicle-search {
  width: 100%;
}

.search-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 1rem;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .form-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.error-container {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
}

/* Search button removed; spinner classes obsolete */
</style>
