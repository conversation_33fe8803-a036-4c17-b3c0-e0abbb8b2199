import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

// Ensure arrays (e.g. region) are serialised as repeated keys, not region[]=
axios.defaults.paramsSerializer = (params) => {
  const search = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((v) => {
        if (v !== undefined && v !== null && v !== '') {
          search.append(key, v)
        }
      })
    } else if (value !== undefined && value !== null && value !== '') {
      search.append(key, value)
    }
  })
  return search.toString()
}

export const useFinderStore = defineStore('finder', () => {
  // State
  const config = ref({})
  const loading = ref(false)
  const error = ref(null)
  const results = ref([])

  // Individual loading states for each API call
  const loadingYears = ref(false)
  const loadingMakes = ref(false)
  const loadingModels = ref(false)
  const loadingGenerations = ref(false)
  const loadingModifications = ref(false)

  // State loaded flags for auto-expand behavior
  const stateLoadedYears = ref(false)
  const stateLoadedMakes = ref(false)
  const stateLoadedModels = ref(false)
  const stateLoadedGenerations = ref(false)
  const stateLoadedModifications = ref(false)

  // Vehicle search state
  const selectedYear = ref('')
  const selectedMake = ref('')
  const selectedModel = ref('')
  const selectedModification = ref('')
  const selectedGeneration = ref('')

  // Available options
  const years = ref([])
  const makes = ref([])
  const models = ref([])
  const modifications = ref([])
  const generations = ref([])

  // Note: Tire and rim search functionality removed
  // Finder-v2 only supports vehicle search (by_vehicle tab)

  // Getters
  const flowType = computed(() => config.value.flowType || 'primary')
  const apiVersion = computed(() => config.value.apiVersion || 'v2')
  const widgetResources = computed(() => config.value.widgetResources || {})

  // Actions
  function initialize(widgetConfig) {
    config.value = widgetConfig
    loadInitialData()
  }

  async function loadInitialData() {
    try {
      loading.value = true
      error.value = null

      // Load years for primary flow or makes for alternative flow
      if (flowType.value === 'primary') {
        await loadYears()
      } else {
        await loadMakes()
      }

      // Note: Finder-v2 only supports vehicle search (by_vehicle tab)
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  async function loadYears() {
    try {
      loadingYears.value = true
      stateLoadedYears.value = false

      const response = await apiCall('year')
      // API response is wrapped in data property: { data: [...] }
      years.value = response.data?.data || response.data || []

      stateLoadedYears.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingYears.value = false
    }
  }

  async function loadMakes(year = null) {
    try {
      loadingMakes.value = true
      stateLoadedMakes.value = false

      const params = year ? { year } : {}

      // NEW: Apply brand filter from widget configuration
      Object.assign(params, buildBrandFilterParams())

      const response = await apiCall('make', params)
      // API response is wrapped in data property: { data: [...] }
      makes.value = response.data?.data || response.data || []

      stateLoadedMakes.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingMakes.value = false
    }
  }

  async function loadModels(make, year = null) {
    try {
      loadingModels.value = true
      stateLoadedModels.value = false

      const params = { make }
      if (year) params.year = year
      // NOTE: Do not include brand filter here - user already selected a specific make
      // Object.assign(params, buildBrandFilterParams())
      const response = await apiCall('model', params)
      // API response is wrapped in data property: { data: [...] }
      models.value = response.data?.data || response.data || []

      stateLoadedModels.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingModels.value = false
    }
  }

  async function loadModifications(make, model, yearOrGeneration = null) {
    try {
      loadingModifications.value = true
      stateLoadedModifications.value = false

      const params = { make, model }

      // Handle both primary flow (year) and alternative flow (generation)
      if (yearOrGeneration) {
        if (flowType.value === 'primary') {
          params.year = yearOrGeneration
        } else {
          params.generation = yearOrGeneration
        }
      }

      const response = await apiCall('modification', params)
      // API response is wrapped in data property: { data: [...] }
      modifications.value = response.data?.data || response.data || []

      stateLoadedModifications.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingModifications.value = false
    }
  }

  async function loadGenerations(make, model) {
    try {
      loadingGenerations.value = true
      stateLoadedGenerations.value = false

      const params = { make, model }
      const response = await apiCall('generation', params)
      // API response is wrapped in data property: { data: [...] }
      generations.value = response.data?.data || response.data || []

      stateLoadedGenerations.value = true
    } catch (err) {
      error.value = err.message
    } finally {
      loadingGenerations.value = false
    }
  }

  // Tire and rim search functions removed - not used in finder-v2

  async function searchByVehicle() {
    try {
      loading.value = true
      error.value = null

      const params = {
        make: selectedMake.value,
        model: selectedModel.value
      }

      if (flowType.value === 'primary') {
        params.year = selectedYear.value
        params.modification = selectedModification.value
      } else {
        params.generation = selectedGeneration.value
        params.modification = selectedModification.value
      }

      const response = await apiCall('search_by_model', params)
      // API response is wrapped in data property: { data: [...] }
      results.value = response.data?.data || response.data || []

      // Trigger iframe resize after results are loaded
      setTimeout(() => {
        if (window.parentIFrame) {
          window.parentIFrame.size()
        }
      }, 100)
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  // searchByTire and searchByRim functions removed - not used in finder-v2

  async function apiCall(endpoint, params = {}) {
    const resource = widgetResources.value[endpoint]
    if (!resource || !resource[1]) {
      throw new Error(`API endpoint not configured: ${endpoint}`)
    }

    let url = resource[1]

    // If URL is relative and we have a baseUrl, make it absolute
    if (url.startsWith('/') && config.value.baseUrl) {
      url = config.value.baseUrl + url
    }

    // Merge region parameters for appropriate endpoints
    // Region filtering applies to make, model, year, generation, modification endpoints
    // but NOT to search_by_model endpoint
    const regionEnabledEndpoints = ['make', 'model', 'year', 'generation', 'modification']
    if (regionEnabledEndpoints.includes(endpoint)) {
      Object.assign(params, buildRegionParams())
    }

    return await axios.get(url, { params })
  }

  function resetVehicleSearch() {
    selectedYear.value = ''
    selectedMake.value = ''
    selectedModel.value = ''
    selectedModification.value = ''
    selectedGeneration.value = ''
    models.value = []
    modifications.value = []
    generations.value = []
  }

  // resetTireSearch and resetRimSearch functions removed - not used in finder-v2

  function clearResults() {
    results.value = []

    // Trigger iframe resize after results are cleared
    setTimeout(() => {
      if (window.parentIFrame) {
        window.parentIFrame.size()
      }
    }, 50)
  }

  // NEW HELPER: returns filter params for API based on configuration
  function buildBrandFilterParams() {
    const filter = config.value?.content?.filter || config.value?.filter || {}
    const by = filter.by || config.value?.content?.by || ''
    const mapToSlug = (item) => (typeof item === 'string' ? item : (item?.slug || item?.value || ''))
    const params = {}
    if (by === 'brands' && Array.isArray(filter.brands) && filter.brands.length) {
      const list = filter.brands.map(mapToSlug).filter(Boolean)
      if (list.length) params.brands = list.join(',')
    } else if (by === 'brands_exclude' && Array.isArray(filter.brands_exclude) && filter.brands_exclude.length) {
      const list = filter.brands_exclude.map(mapToSlug).filter(Boolean)
      if (list.length) params.brands_exclude = list.join(',')
    }
    return params
  }

  // NEW HELPER: returns region params for API based on configuration
  function buildRegionParams() {
    const regions = config.value?.content?.regions || []
    return regions.length ? { region: regions } : {}
  }

  return {
    // State
    config,
    loading,
    error,
    results,

    // Individual loading states
    loadingYears,
    loadingMakes,
    loadingModels,
    loadingGenerations,
    loadingModifications,

    // State loaded flags
    stateLoadedYears,
    stateLoadedMakes,
    stateLoadedModels,
    stateLoadedGenerations,
    stateLoadedModifications,

    // Vehicle search (only supported search type)
    selectedYear,
    selectedMake,
    selectedModel,
    selectedModification,
    selectedGeneration,
    years,
    makes,
    models,
    modifications,
    generations,

    // Getters
    flowType,
    apiVersion,
    widgetResources,

    // Actions
    initialize,
    loadYears,
    loadMakes,
    loadModels,
    loadModifications,
    loadGenerations,
    searchByVehicle,
    resetVehicleSearch,
    clearResults,
    buildBrandFilterParams,
    buildRegionParams
  }
})
