/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    // Remove unused template scanning - widget doesn't use external templates
    // "../templates/**/*.html"
  ],

  // Minimal safelist - only include classes that cannot be detected by content scanning
  // Most classes are automatically detected from Vue components
  safelist: [
    // Dynamic classes that might be constructed programmatically
    // (Only include if you have dynamic class construction in your Vue components)

    // Ensure ws-secondary color classes are available for generation display formatting
    'text-ws-secondary-500',

    // TailwindUI reference template classes for CustomSelector component
    'grid', 'grid-cols-1', 'col-start-1', 'row-start-1', 'gap-2', 'pr-6', 'pr-9',
    'size-5', 'size-4', 'self-center', 'justify-self-end', 'sm:size-4', 'sm:text-sm/6',
    'outline-1', '-outline-offset-1', 'outline-gray-300', 'focus:outline-2',
    'focus:-outline-offset-2', 'focus:outline-indigo-600', 'ring-black/5',
    'focus:outline-hidden', 'font-semibold', 'text-indigo-600', 'text-indigo-200',
    'bg-indigo-600', 'text-white', 'outline-hidden',

    // Border fallback for CustomSelector visibility
    'border', 'border-gray-300', 'focus:border-indigo-600',

    // Iframe content padding
    'p-2', 'p-1',

    // Note: Most classes are now detected automatically from Vue component templates
    // This safelist should remain as minimal as possible
  ]
}
