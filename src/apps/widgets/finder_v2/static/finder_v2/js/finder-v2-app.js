var e=(e,a,l)=>new Promise((t,o)=>{var n=e=>{try{i(l.next(e))}catch(a){o(a)}},s=e=>{try{i(l.throw(e))}catch(a){o(a)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,s);i((l=l.apply(e,a)).next())});import{a,d as l,r as t,c as o,b as n,e as s,o as i,F as d,A as r,j as u,I as c,w as p,f as m,g,h as f,i as v,k as h,l as y,n as w,m as b,p as M,t as k,T as x,q as V,s as C,u as L,v as _,x as S,y as z,z as G}from"./listbox-DS9H9vH4.js";a.defaults.paramsSerializer=e=>{const a=new URLSearchParams;return Object.entries(e).forEach(([e,l])=>{Array.isArray(l)?l.forEach(l=>{null!=l&&""!==l&&a.append(e,l)}):null!=l&&""!==l&&a.append(e,l)}),a.toString()};const F=l("finder",()=>{const l=t({}),n=t(!1),s=t(null),i=t([]),d=t(!1),r=t(!1),u=t(!1),c=t(!1),p=t(!1),m=t(!1),g=t(!1),f=t(!1),v=t(!1),h=t(!1),y=t(""),w=t(""),b=t(""),M=t(""),k=t(""),x=t([]),V=t([]),C=t([]),L=t([]),_=t([]),S=o(()=>l.value.flowType||"primary"),z=o(()=>l.value.apiVersion||"v2"),G=o(()=>l.value.widgetResources||{});function F(){return e(this,null,function*(){var e;try{d.value=!0,m.value=!1;const a=yield T("year");x.value=(null==(e=a.data)?void 0:e.data)||a.data||[],m.value=!0}catch(a){s.value=a.message}finally{d.value=!1}})}function R(a=null){return e(this,null,function*(){var e;try{r.value=!0,g.value=!1;const l=a?{year:a}:{};Object.assign(l,D());const t=yield T("make",l);V.value=(null==(e=t.data)?void 0:e.data)||t.data||[],g.value=!0}catch(l){s.value=l.message}finally{r.value=!1}})}function T(t){return e(this,arguments,function*(e,t={}){const o=G.value[e];if(!o||!o[1])throw new Error(`API endpoint not configured: ${e}`);let n=o[1];n.startsWith("/")&&l.value.baseUrl&&(n=l.value.baseUrl+n);return["make","model","year","generation","modification"].includes(e)&&Object.assign(t,I()),yield a.get(n,{params:t})})}function D(){var e,a,t,o,n;const s=(null==(a=null==(e=l.value)?void 0:e.content)?void 0:a.filter)||(null==(t=l.value)?void 0:t.filter)||{},i=s.by||(null==(n=null==(o=l.value)?void 0:o.content)?void 0:n.by)||"",d=e=>"string"==typeof e?e:(null==e?void 0:e.slug)||(null==e?void 0:e.value)||"",r={};if("brands"===i&&Array.isArray(s.brands)&&s.brands.length){const e=s.brands.map(d).filter(Boolean);e.length&&(r.brands=e.join(","))}else if("brands_exclude"===i&&Array.isArray(s.brands_exclude)&&s.brands_exclude.length){const e=s.brands_exclude.map(d).filter(Boolean);e.length&&(r.brands_exclude=e.join(","))}return r}function I(){var e,a;const t=(null==(a=null==(e=l.value)?void 0:e.content)?void 0:a.regions)||[];return t.length?{region:t}:{}}return{config:l,loading:n,error:s,results:i,loadingYears:d,loadingMakes:r,loadingModels:u,loadingGenerations:c,loadingModifications:p,stateLoadedYears:m,stateLoadedMakes:g,stateLoadedModels:f,stateLoadedGenerations:v,stateLoadedModifications:h,selectedYear:y,selectedMake:w,selectedModel:b,selectedModification:M,selectedGeneration:k,years:x,makes:V,models:C,modifications:L,generations:_,flowType:S,apiVersion:z,widgetResources:G,initialize:function(a){l.value=a,function(){e(this,null,function*(){try{n.value=!0,s.value=null,"primary"===S.value?yield F():yield R()}catch(e){s.value=e.message}finally{n.value=!1}})}()},loadYears:F,loadMakes:R,loadModels:function(a,l=null){return e(this,null,function*(){var e;try{u.value=!0,f.value=!1;const t={make:a};l&&(t.year=l);const o=yield T("model",t);C.value=(null==(e=o.data)?void 0:e.data)||o.data||[],f.value=!0}catch(t){s.value=t.message}finally{u.value=!1}})},loadModifications:function(a,l,t=null){return e(this,null,function*(){var e;try{p.value=!0,h.value=!1;const o={make:a,model:l};t&&("primary"===S.value?o.year=t:o.generation=t);const n=yield T("modification",o);L.value=(null==(e=n.data)?void 0:e.data)||n.data||[],h.value=!0}catch(o){s.value=o.message}finally{p.value=!1}})},loadGenerations:function(a,l){return e(this,null,function*(){var e;try{c.value=!0,v.value=!1;const t={make:a,model:l},o=yield T("generation",t);_.value=(null==(e=o.data)?void 0:e.data)||o.data||[],v.value=!0}catch(t){s.value=t.message}finally{c.value=!1}})},searchByVehicle:function(){return e(this,null,function*(){var e;try{n.value=!0,s.value=null;const a={make:w.value,model:b.value};"primary"===S.value?(a.year=y.value,a.modification=M.value):(a.generation=k.value,a.modification=M.value);const l=yield T("search_by_model",a);i.value=(null==(e=l.data)?void 0:e.data)||l.data||[],setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},100)}catch(a){s.value=a.message}finally{n.value=!1}})},resetVehicleSearch:function(){y.value="",w.value="",b.value="",M.value="",k.value="",C.value=[],L.value=[],_.value=[]},clearResults:function(){i.value=[],setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},50)},buildBrandFilterParams:D,buildRegionParams:I}});const R=(e,a)=>{const l=e.__vccOpts||e;for(const[t,o]of a)l[t]=o;return l},T={name:"CustomSelector",components:{Listbox:c,ListboxButton:u,ListboxOptions:r,ListboxOption:d,CheckIcon:function(e,a){return i(),n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z","clip-rule":"evenodd"})])},ChevronUpDownIcon:function(e,a){return i(),n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"fill-rule":"evenodd",d:"M10.53 3.47a.75.75 0 0 0-1.06 0L6.22 6.72a.75.75 0 0 0 1.06 1.06L10 5.06l2.72 2.72a.75.75 0 1 0 1.06-1.06l-3.25-3.25Zm-4.31 9.81 3.25 3.25a.75.75 0 0 0 1.06 0l3.25-3.25a.75.75 0 1 0-1.06-1.06L10 14.94l-2.72-2.72a.75.75 0 0 0-1.06 1.06Z","clip-rule":"evenodd"})])}},props:{modelValue:{type:[String,Number],default:""},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"Select option"},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},preloader:{type:Boolean,default:!1},stateLoaded:{type:Boolean,default:!1},autoExpand:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(a,{emit:l}){const n=t(null),s=o({get:()=>a.modelValue,set:e=>{l("update:modelValue",e),l("change",e)}}),i=o(()=>a.modelValue?a.options.find(e=>(e.slug||e.id)===a.modelValue):null);p(()=>a.options,e=>{if(a.modelValue&&e.length>0){e.some(e=>(e.slug||e.id)===a.modelValue)||(s.value="")}});const d=window.matchMedia("(prefers-reduced-motion: reduce)").matches;p(()=>[a.stateLoaded,a.options.length],([l,t],[o])=>{var s,i;if(!o&&l&&t>0&&!a.disabled&&a.autoExpand&&!d){const l=document.activeElement,o=null!=(i=null==(s=n.value)?void 0:s.$el)?i:n.value;if(l&&l!==document.body&&o&&!o.contains(l)&&["INPUT","TEXTAREA"].includes(l.tagName))return;console.debug("AutoExpand →",{placeholder:a.placeholder,len:t}),setTimeout(()=>e(this,null,function*(){var e,l;yield w();const t=null!=(l=null==(e=n.value)?void 0:e.$el)?l:n.value;try{t&&"function"==typeof t.click&&(t.focus({preventScroll:!0}),t.click(),console.debug("AutoExpand click dispatched",{placeholder:a.placeholder}),r())}catch(o){console.warn("Auto-expand failed:",o)}}),200)}},{flush:"post"});const r=()=>{window.parentIFrame&&window.parentIFrame.size&&(window.parentIFrame.size(),setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},350))};return p(()=>a.modelValue,()=>{r()}),m(()=>{var e;n.value&&(null!=(e=n.value.$el)?e:n.value).addEventListener("click",r)}),g(()=>{var e;n.value&&(null!=(e=n.value.$el)?e:n.value).removeEventListener("click",r)}),{selectedValue:s,selectedOption:i,getDisplayText:e=>{if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const a=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?`${e.name}, ${a}`:a}return e.name||""},getDisplayData:e=>{if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const a=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?{isGeneration:!0,name:e.name,yearRanges:a}:{isGeneration:!1,name:a,yearRanges:""}}return{isGeneration:!1,name:e.name||"",yearRanges:""}},buttonRef:n,triggerResize:r}}},D={class:"flex items-start"},I={class:"relative"},A={key:0,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},B={key:1,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},Y={class:"truncate"},O={class:"truncate text-ws-secondary-500"},E={key:1,class:"truncate"},U={key:2,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},j={class:"flex"},P={class:"min-w-[32px] mt-2 ml-1"},$={key:0,class:"spinner-external"};const N={class:"vehicle-search"},H={key:0,class:"form-grid"},W={class:"form-group"},Z={class:"form-group"},X={class:"form-group"},q={class:"form-group"},K={key:1,class:"form-grid"},J={class:"form-group"},Q={class:"form-group"},ee={class:"form-group"},ae={class:"form-group"},le={key:0,class:"error-container"},te={class:"error"};const oe={class:"results-display"},ne={key:0,class:"loading"},se={key:1,class:"no-results"},ie={key:2,class:"results-content"},de={class:"results-header"},re={class:"results-title"},ue={class:"table-container"},ce={class:"results-table"},pe={class:"font-medium"},me={key:0},ge={key:1},fe={key:0},ve={key:1},he={key:0},ye={key:1},we={key:0},be={key:1},Me={key:0,class:"wheel-size-button-container"},ke=["href"];const xe={class:"finder-v2-widget p-1","data-iframe-height":""},Ve={class:"search-content"},Ce={key:0,class:"results-section"};const Le=z(R({name:"FinderV2Widget",components:{VehicleSearch:R({name:"VehicleSearch",components:{CustomSelector:R(T,[["render",function(e,a,l,t,o,d){const r=y("ListboxButton"),u=y("ListboxOption"),c=y("CheckIcon"),p=y("ListboxOptions"),m=y("Listbox");return i(),n("div",D,[f(m,{modelValue:t.selectedValue,"onUpdate:modelValue":a[0]||(a[0]=e=>t.selectedValue=e),disabled:l.disabled,class:"flex-1"},{default:v(()=>[s("div",I,[f(r,{as:"button",class:b(["grid w-full cursor-default grid-cols-1 rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 border border-gray-300 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 focus:border-indigo-600 sm:text-sm/6",l.disabled?"bg-gray-100 text-gray-500 cursor-not-allowed":""]),ref:"buttonRef"},{default:v(()=>[l.loading?(i(),n("span",A,a[1]||(a[1]=[s("svg",{class:"animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"truncate"},"Loading...",-1)]))):t.selectedOption?(i(),n("span",B,[t.getDisplayData(t.selectedOption).isGeneration?(i(),n(M,{key:0},[s("span",Y,k(t.getDisplayData(t.selectedOption).name),1),s("span",O,k(t.getDisplayData(t.selectedOption).yearRanges),1)],64)):(i(),n("span",E,k(t.getDisplayText(t.selectedOption)),1))])):(i(),n("span",U,[s("span",{class:b(["truncate",l.disabled?"text-gray-500":"text-gray-900"])},k(l.placeholder),3)])),a[2]||(a[2]=s("svg",{class:"col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 sm:size-4",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true"},[s("path",{"fill-rule":"evenodd",d:"M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z","clip-rule":"evenodd"})],-1))]),_:1,__:[2]},8,["class"]),f(x,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:v(()=>[f(p,{class:"z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base ring-1 shadow-lg ring-black/5 focus:outline-hidden sm:text-sm"},{default:v(()=>[l.options.length||l.loading?h("",!0):(i(),V(u,{key:0,value:null,class:"relative cursor-default select-none py-2 pr-9 pl-3 text-gray-500",disabled:""},{default:v(()=>a[3]||(a[3]=[C(" No options available ")])),_:1,__:[3]})),(i(!0),n(M,null,L(l.options,e=>(i(),V(u,{key:e.slug||e.id,value:e.slug||e.id,as:"template"},{default:v(({active:a,selected:l})=>[s("li",{class:b([a?"bg-indigo-600 text-white outline-hidden":"text-gray-900","relative cursor-default select-none py-2 pr-9 pl-3"])},[s("div",j,[t.getDisplayData(e).isGeneration?(i(),n(M,{key:0},[s("span",{class:b([l?"font-semibold":"font-normal","truncate"])},k(t.getDisplayData(e).name),3),s("span",{class:b([a?"text-indigo-200":"text-ws-secondary-500","ml-2 truncate"])},k(t.getDisplayData(e).yearRanges),3)],64)):(i(),n("span",{key:1,class:b([l?"font-semibold":"font-normal","truncate"])},k(t.getDisplayText(e)),3))]),l?(i(),n("span",{key:0,class:b([a?"text-white":"text-indigo-600","absolute inset-y-0 right-0 flex items-center pr-4"])},[f(c,{class:"size-5","aria-hidden":"true"})],2)):h("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue","disabled"]),s("div",P,[l.preloader?(i(),n("div",$,a[4]||(a[4]=[s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-5 h-5 text-gray-400"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]))):h("",!0)])])}],["__scopeId","data-v-371bc297"]])},setup(){const a=F(),{loading:l,error:t,selectedYear:n,selectedMake:s,selectedModel:i,selectedModification:d,selectedGeneration:r,years:u,makes:c,models:m,modifications:g,generations:f,flowType:v,loadingYears:h,loadingMakes:y,loadingModels:w,loadingGenerations:b,loadingModifications:M,stateLoadedYears:k,stateLoadedMakes:x,stateLoadedModels:V,stateLoadedGenerations:C,stateLoadedModifications:L}=_(a),S=o(()=>"primary"===v.value?n.value&&s.value&&i.value&&d.value:s.value&&i.value&&r.value&&d.value);function z(){return e(this,null,function*(){S.value&&(yield a.searchByVehicle())})}return p(S,(a,l)=>e(this,null,function*(){a&&!l&&(yield z())})),p(d,(a,t)=>e(this,null,function*(){a&&a!==t&&!l.value&&(yield z())})),{loading:l,error:t,selectedYear:n,selectedMake:s,selectedModel:i,selectedModification:d,selectedGeneration:r,years:u,makes:c,models:m,modifications:g,generations:f,flowType:v,loadingYears:h,loadingMakes:y,loadingModels:w,loadingGenerations:b,loadingModifications:M,stateLoadedYears:k,stateLoadedMakes:x,stateLoadedModels:V,stateLoadedGenerations:C,stateLoadedModifications:L,canSearch:S,onYearChange:function(){return e(this,null,function*(){a.selectedMake="",a.selectedModel="",a.selectedModification="",a.models=[],a.modifications=[],n.value&&(yield a.loadMakes(n.value))})},onMakeChange:function(){return e(this,null,function*(){a.selectedModel="",a.selectedModification="",a.selectedGeneration="",a.models=[],a.modifications=[],a.generations=[],s.value&&("primary"===v.value?yield a.loadModels(s.value,n.value):yield a.loadModels(s.value))})},onModelChange:function(){return e(this,null,function*(){a.selectedModification="",a.selectedGeneration="",a.modifications=[],a.generations=[],i.value&&("primary"===v.value?yield a.loadModifications(s.value,i.value,n.value):yield a.loadGenerations(s.value,i.value))})},onGenerationChange:function(){return e(this,null,function*(){a.selectedModification="",a.modifications=[],r.value&&(yield a.loadModifications(s.value,i.value,r.value))})},handleSearch:z}}},[["render",function(e,a,l,t,o,d){const r=y("CustomSelector");return i(),n("div",N,[a[17]||(a[17]=s("h4",{class:"search-title"}," Specify make, year and model to find matching wheels: ",-1)),s("form",{onSubmit:a[8]||(a[8]=S((...e)=>t.handleSearch&&t.handleSearch(...e),["prevent"])),class:"search-form"},["primary"===t.flowType?(i(),n("div",H,[s("div",W,[a[9]||(a[9]=s("label",{class:"form-label"},"Year",-1)),f(r,{modelValue:t.selectedYear,"onUpdate:modelValue":a[0]||(a[0]=e=>t.selectedYear=e),options:t.years,loading:t.loading&&!t.years.length,preloader:t.loadingYears,"state-loaded":t.stateLoadedYears,"auto-expand":!1,placeholder:"Select Year",onChange:t.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","onChange"])]),s("div",Z,[a[10]||(a[10]=s("label",{class:"form-label"},"Make",-1)),f(r,{modelValue:t.selectedMake,"onUpdate:modelValue":a[1]||(a[1]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&t.selectedYear&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,disabled:!t.selectedYear,placeholder:"Select Make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),s("div",X,[a[11]||(a[11]=s("label",{class:"form-label"},"Model",-1)),f(r,{modelValue:t.selectedModel,"onUpdate:modelValue":a[2]||(a[2]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake,placeholder:"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),s("div",q,[a[12]||(a[12]=s("label",{class:"form-label"},"Modification",-1)),f(r,{modelValue:t.selectedModification,"onUpdate:modelValue":a[3]||(a[3]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedModel&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedModel,placeholder:"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled"])])])):(i(),n("div",K,[s("div",J,[a[13]||(a[13]=s("label",{class:"form-label"},"Make",-1)),f(r,{modelValue:t.selectedMake,"onUpdate:modelValue":a[4]||(a[4]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,"auto-expand":!1,placeholder:"Select Make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","onChange"])]),s("div",Q,[a[14]||(a[14]=s("label",{class:"form-label"},"Model",-1)),f(r,{modelValue:t.selectedModel,"onUpdate:modelValue":a[5]||(a[5]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake,placeholder:"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),s("div",ee,[a[15]||(a[15]=s("label",{class:"form-label"},"Generation",-1)),f(r,{modelValue:t.selectedGeneration,"onUpdate:modelValue":a[6]||(a[6]=e=>t.selectedGeneration=e),options:t.generations,loading:t.loading&&t.selectedModel&&!t.generations.length,preloader:t.loadingGenerations,"state-loaded":t.stateLoadedGenerations,disabled:!t.selectedModel,placeholder:"Select Generation",onChange:t.onGenerationChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),s("div",ae,[a[16]||(a[16]=s("label",{class:"form-label"},"Modification",-1)),f(r,{modelValue:t.selectedModification,"onUpdate:modelValue":a[7]||(a[7]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedGeneration&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedGeneration,placeholder:"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled"])])]))],32),t.error?(i(),n("div",le,[s("p",te,k(t.error),1)])):h("",!0)])}],["__scopeId","data-v-87b61f65"]]),ResultsDisplay:R({name:"ResultsDisplay",setup(){const e=F(),{loading:a,results:l,config:t}=_(e);return{loading:a,results:l,showWheelSizeButton:o(()=>{var e,a,l;return!(null==(l=null==(a=null==(e=t.value.widgetConfig)?void 0:e.blocks)?void 0:a.button_to_ws)?void 0:l.hide)}),wheelSizeUrl:o(()=>{var e;return`https://www.wheel-size.com${(null==(e=t.value.widgetConfig)?void 0:e.utm)||""}`})}}},[["render",function(e,a,l,t,o,d){return i(),n("div",oe,[t.loading?(i(),n("div",ne,a[0]||(a[0]=[s("svg",{class:"spinner",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"ml-2"},"Loading results...",-1)]))):0===t.results.length?(i(),n("div",se,a[1]||(a[1]=[s("p",{class:"no-results-text"},"No results found. Please try different search criteria.",-1)]))):(i(),n("div",ie,[s("div",de,[s("h5",re," Search Results ("+k(t.results.length)+" "+k(1===t.results.length?"result":"results")+") ",1),a[2]||(a[2]=s("p",{class:"results-note"},[C(" Please note: "),s("span",{class:"oem-highlight"},"highlighted"),C(" items are for OEM wheels ")],-1))]),s("div",ue,[s("table",ce,[a[3]||(a[3]=s("thead",null,[s("tr",null,[s("th",null,"Vehicle"),s("th",null,"Year"),s("th",null,"Trim"),s("th",null,"Tire"),s("th",null,"Rim"),s("th",null,"THD"),s("th",null,"CB")])],-1)),s("tbody",null,[(i(!0),n(M,null,L(t.results,(e,a)=>(i(),n("tr",{key:a,class:b({oem:e.is_oem})},[s("td",pe,k(e.make)+" "+k(e.model),1),s("td",null,k(e.year),1),s("td",null,k(e.trim||"-"),1),s("td",null,[e.tire?(i(),n("span",me,k(e.tire.width)+"/"+k(e.tire.aspect_ratio)+"R"+k(e.tire.diameter),1)):(i(),n("span",ge,"-"))]),s("td",null,[e.rim?(i(),n("span",fe,k(e.rim.diameter)+"x"+k(e.rim.width),1)):(i(),n("span",ve,"-"))]),s("td",null,[e.rim&&e.rim.bolt_pattern?(i(),n("span",he,k(e.rim.bolt_pattern),1)):(i(),n("span",ye,"-"))]),s("td",null,[e.rim&&e.rim.center_bore?(i(),n("span",we,k(e.rim.center_bore),1)):(i(),n("span",be,"-"))])],2))),128))])])]),t.showWheelSizeButton?(i(),n("div",Me,[s("a",{href:t.wheelSizeUrl,target:"_blank",rel:"noopener noreferrer",class:"btn btn-secondary"}," See on Wheel-Size.com ",8,ke)])):h("",!0)]))])}],["__scopeId","data-v-c2f53eae"]])},setup(){const e=F(),a=window.FinderV2Config||{},l=o(()=>e.results.length>0);return m(()=>{e.initialize(a)}),{hasResults:l}}},[["render",function(e,a,l,t,o,d){const r=y("VehicleSearch"),u=y("ResultsDisplay");return i(),n("div",xe,[s("div",Ve,[f(r)]),t.hasResults?(i(),n("div",Ce,[f(u)])):h("",!0)])}],["__scopeId","data-v-c3a0a023"]]));function _e(){window.parent&&window.parent!==window&&window.parentIFrame&&window.parentIFrame.size()}Le.use(G()),document.addEventListener("DOMContentLoaded",()=>{var e,l,t;a.defaults.headers.common["X-CSRF-TOKEN"]=(null==(e=window.FinderV2Config)?void 0:e.csrfToken)||"",console.log("CSRF token configured:",(null==(l=window.FinderV2Config)?void 0:l.csrfToken)||"NOT FOUND"),Le.config.globalProperties.$config=window.FinderV2Config||{};const o=document.getElementById("finder-v2-app");o?(Le.mount(o),(null==(t=window.FinderV2Config)?void 0:t.iframeResize)&&setTimeout(()=>{if(window.parentIFrame){_e();new MutationObserver(()=>{setTimeout(_e,50)}).observe(o,{childList:!0,subtree:!0,attributes:!0}),window.addEventListener("resize",_e)}},100)):console.error("Finder-v2 widget container not found")}),window.FinderV2App=Le;
