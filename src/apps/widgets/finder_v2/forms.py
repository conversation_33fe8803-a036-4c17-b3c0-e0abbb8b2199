import json

from django import forms
from django.urls import reverse
from django.test import RequestFactory, Client
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from rest_framework import status

from src.apps.widgets.api_proxy.views import FinderV2WidgetProxyView
from src.apps.widgets.common.forms import FakeModelForm, WidgetPermissionsForm, WidgetCommonForm, \
    WidgetInterfaceForm, WidgetConfigForm, DemoWidgetCommonForm
from src.apps.widgets.common.fields import WsJsonFormField


class DemoCompatibleJSO<PERSON>ield(forms.CharField):
    """
    Custom field that handles both comma-separated strings (demo template)
    and JSON arrays (storage format). Inherits from CharField to avoid
    JSONField's automatic JSON parsing issues.
    """

    def __init__(self, **kwargs):
        # Set default widget to TextInput for demo template compatibility
        kwargs.setdefault('widget', forms.TextInput)
        super().__init__(**kwargs)

    def to_python(self, value):
        """
        Convert the value to Python format (list).
        Handles both comma-separated strings and JSON arrays.
        """
        if not value:
            return []

        if isinstance(value, list):
            return value

        if isinstance(value, str):
            # Try JSON first
            try:
                parsed = json.loads(value)
                if isinstance(parsed, list):
                    return parsed
                else:
                    return [parsed] if parsed else []
            except (json.JSONDecodeError, TypeError):
                # Not JSON, treat as comma-separated string
                if ',' in value:
                    return [item.strip() for item in value.split(',') if item.strip()]
                elif value.strip():
                    return [value.strip()]
                else:
                    return []

        return []

    def prepare_value(self, value):
        """
        Convert the value for display in the form field.
        This is called when rendering the form field in templates.
        CRITICAL: Always return comma-separated string format for demo template compatibility.
        """
        import logging
        logger = logging.getLogger(__name__)
        
        logger.debug(f"🔍 PREPARE_VALUE: Input value: {value} (type: {type(value)})")
        
        # Handle None or empty values
        if not value:
            logger.debug(f"🔍 PREPARE_VALUE: Empty value, returning empty string")
            return ''
        
        if isinstance(value, list):
            # Handle nested list format from Django form processing
            if len(value) == 1 and isinstance(value[0], str):
                # Single item list containing a string (possibly JSON)
                single_value = value[0]
                logger.debug(f"🔍 PREPARE_VALUE: Single-item list, processing inner value: {single_value}")
                
                try:
                    # Try to parse the inner string as JSON
                    parsed = json.loads(single_value)
                    if isinstance(parsed, list):
                        result = ','.join(str(item) for item in parsed) if parsed else ''
                        logger.debug(f"🔍 PREPARE_VALUE: Nested JSON array -> comma-separated: {result}")
                        return result
                    else:
                        result = str(parsed) if parsed else ''
                        logger.debug(f"🔍 PREPARE_VALUE: Nested JSON value -> string: {result}")
                        return result
                except (json.JSONDecodeError, TypeError):
                    # Not JSON, treat as regular string
                    result = single_value
                    logger.debug(f"🔍 PREPARE_VALUE: Nested string -> as-is: {result}")
                    return result
            else:
                # Regular list - convert to comma-separated string
                result = ','.join(str(item) for item in value) if value else ''
                logger.debug(f"🔍 PREPARE_VALUE: List -> comma-separated: {result}")
                return result
            
        elif isinstance(value, str):
            # Handle string data (could be JSON or comma-separated)
            if not value.strip():
                logger.debug(f"🔍 PREPARE_VALUE: Empty string, returning empty")
                return ''
                
            try:
                # Try to parse as JSON first
                parsed = json.loads(value)
                logger.debug(f"🔍 PREPARE_VALUE: Parsed JSON: {parsed} (type: {type(parsed)})")
                
                if isinstance(parsed, list):
                    # Convert JSON array to comma-separated string for demo template
                    result = ','.join(str(item) for item in parsed) if parsed else ''
                    logger.debug(f"🔍 PREPARE_VALUE: JSON array -> comma-separated: {result}")
                    return result
                else:
                    # Single value
                    result = str(parsed) if parsed else ''
                    logger.debug(f"🔍 PREPARE_VALUE: JSON single value -> string: {result}")
                    return result
            except (json.JSONDecodeError, TypeError):
                # Not JSON, assume it's already comma-separated string
                logger.debug(f"🔍 PREPARE_VALUE: Not JSON, returning as-is: {value}")
                return value
        else:
            # Convert any other type to string
            result = str(value)
            logger.debug(f"🔍 PREPARE_VALUE: Other type -> string: {result}")
            return result

    def validate(self, value):
        """
        Custom validation to ensure the value is a valid list.
        """
        super().validate(value)
        if value and not isinstance(value, list):
            raise forms.ValidationError('Value must be a list.')

    def clean(self, value):
        """
        Clean the value and convert it to the proper format.
        """
        value = self.to_python(value)
        self.validate(value)
        return value
from src.apps.widgets.finder_v2.default_config.themes import FinderV2Themes
from src.apps.widgets.finder_v2.models import FinderV2InterfaceTabs


class ContentFilterForm(FakeModelForm):
    CHOICES = (
        ('brands', _('Brands')),
        ('brands_exclude', _('Exclude brands')),
    )

    brands = DemoCompatibleJSONField(label=_('Brands'), required=False)
    brands_exclude = DemoCompatibleJSONField(label=_('Exclude brands'), required=False)

    by = forms.ChoiceField(choices=CHOICES, required=False)

    regions = DemoCompatibleJSONField(label=_('Regions'), required=False)

    only_oem = forms.BooleanField(label=_('Show only Original Equipment (OE) modifications'),
                                  initial=False,
                                  required=False)

    filter_choices = [field[0] for field in CHOICES]

    # Use DefaultJson wrapper for proper data handling
    use_default_json = True

    def __getitem__(self, name):
        """Override to ensure BoundField.value() returns correct format for JSON fields."""
        bound_field = super().__getitem__(name)
        
        # For DemoCompatibleJSONField, ensure the value is always in the correct format
        if isinstance(self.fields[name], DemoCompatibleJSONField):
            original_value_method = bound_field.value
            
            def custom_value():
                # Get the raw value using original method
                raw_value = original_value_method()
                # Apply prepare_value to ensure correct format
                return self.fields[name].prepare_value(raw_value)
            
            # Replace the value method with our custom one
            bound_field.value = custom_value
        
        return bound_field

    def __init__(self, *args, **kwargs):
        import logging
        import threading
        logger = logging.getLogger(__name__)

        # Get thread/request ID for correlation
        thread_id = threading.get_ident()

        self.widget = kwargs.pop('widget', None)

        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: ContentFilterForm.__init__ called")
        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: args={args}")
        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: kwargs keys={list(kwargs.keys())}")

        # CRITICAL: Fix malformed JSON data BEFORE Django form initialization
        # Data can be passed as args[0] OR kwargs['data']
        data = None
        if args and len(args) > 0:
            data = args[0]
            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Found data in args[0]")
        elif 'data' in kwargs:
            data = kwargs['data']
            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Found data in kwargs['data']")

        if data is not None:
            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Raw POST data type: {type(data)}")
            if hasattr(data, 'items'):
                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Raw POST data: {dict(data)}")

                # Process all JSON fields that might have malformed data
                json_fields = ['content-regions', 'content-brands', 'content-brands_exclude']

                for field_name in json_fields:
                    if field_name in data:
                        raw_field_data = data[field_name]
                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Raw {field_name} in POST: {raw_field_data} (type: {type(raw_field_data)})")

                        # Handle list data (Django QueryDict format)
                        if isinstance(raw_field_data, list) and len(raw_field_data) > 0:
                            raw_string = raw_field_data[0]
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Raw {field_name} string: '{raw_string}' (type: {type(raw_string)})")

                            if raw_string and isinstance(raw_string, str):
                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Attempting JSON validation for {field_name}: '{raw_string}'")
                                try:
                                    # Try to parse as JSON first
                                    parsed = json.loads(raw_string)
                                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: {field_name} data is already valid JSON: {parsed}")
                                except json.JSONDecodeError as e:
                                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: MALFORMED JSON DETECTED in {field_name}: '{raw_string}' - Error: {e}")

                                    # Handle specific case like "[],usdm,cdm"
                                    if ',' in raw_string:
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Processing comma-separated values in {field_name}")
                                        parts = [part.strip() for part in raw_string.split(',') if part.strip() and part.strip() != '[]']
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Extracted parts from {field_name}: {parts}")
                                        if parts:
                                            fixed_json = json.dumps(parts)
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: FIXING MALFORMED DATA in {field_name}: '{raw_string}' -> '{fixed_json}'")

                                            # Create mutable copy and fix the data
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Making data mutable for {field_name}")

                                            # CRITICAL: Create a new mutable QueryDict if needed
                                            from django.http import QueryDict
                                            if isinstance(data, QueryDict) and not data._mutable:
                                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict")
                                                new_data = data.copy()
                                                new_data._mutable = True
                                                data = new_data
                                                # Update the reference in args or kwargs
                                                if args and len(args) > 0:
                                                    args = (data,) + args[1:]
                                                elif 'data' in kwargs:
                                                    kwargs['data'] = data
                                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Updated data reference")
                                            elif hasattr(data, '_mutable'):
                                                data._mutable = True
                                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Data._mutable set to True")

                                            data[field_name] = [fixed_json]
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: DATA FIXED - new {field_name}: {data[field_name]}")
                                        else:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: No valid parts found in {field_name}, setting to empty array")

                                            # CRITICAL: Create a new mutable QueryDict if needed
                                            from django.http import QueryDict
                                            if isinstance(data, QueryDict) and not data._mutable:
                                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for empty array")
                                                new_data = data.copy()
                                                new_data._mutable = True
                                                data = new_data
                                                # Update the reference in args or kwargs
                                                if args and len(args) > 0:
                                                    args = (data,) + args[1:]
                                                elif 'data' in kwargs:
                                                    kwargs['data'] = data
                                            elif hasattr(data, '_mutable'):
                                                data._mutable = True

                                            data[field_name] = ['[]']
                            else:
                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Skipping {field_name} - not a valid string: '{raw_string}' (type: {type(raw_string)})")
                        elif isinstance(raw_field_data, str):
                            # Handle case where data is directly a string (not in a list)
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Processing direct string for {field_name}: '{raw_field_data}'")
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Attempting JSON validation for {field_name}: '{raw_field_data}'")
                            try:
                                # Try to parse as JSON first
                                parsed = json.loads(raw_field_data)
                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: {field_name} data is already valid JSON: {parsed}")
                            except json.JSONDecodeError as e:
                                logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: MALFORMED JSON DETECTED in {field_name}: '{raw_field_data}' - Error: {e}")

                                # Handle specific case like "[],usdm,cdm"
                                if ',' in raw_field_data:
                                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Processing comma-separated values in {field_name}")
                                    parts = [part.strip() for part in raw_field_data.split(',') if part.strip() and part.strip() != '[]']
                                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Extracted parts from {field_name}: {parts}")
                                    if parts:
                                        fixed_json = json.dumps(parts)
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: FIXING MALFORMED DATA in {field_name}: '{raw_field_data}' -> '{fixed_json}'")

                                        # Create mutable copy and fix the data
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Making data mutable for {field_name}")

                                        # CRITICAL: Create a new mutable QueryDict if needed
                                        from django.http import QueryDict
                                        if isinstance(data, QueryDict) and not data._mutable:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for string data")
                                            new_data = data.copy()
                                            new_data._mutable = True
                                            data = new_data
                                            # Update the reference in args or kwargs
                                            if args and len(args) > 0:
                                                args = (data,) + args[1:]
                                            elif 'data' in kwargs:
                                                kwargs['data'] = data
                                        elif hasattr(data, '_mutable'):
                                            data._mutable = True
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Data._mutable set to True")

                                        data[field_name] = [fixed_json]  # Store as list for Django QueryDict compatibility
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: DATA FIXED - new {field_name}: {data[field_name]}")
                                    else:
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: No valid parts found in {field_name}, setting to empty array")

                                        # CRITICAL: Create a new mutable QueryDict if needed
                                        from django.http import QueryDict
                                        if isinstance(data, QueryDict) and not data._mutable:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for string empty array")
                                            new_data = data.copy()
                                            new_data._mutable = True
                                            data = new_data
                                            # Update the reference in args or kwargs
                                            if args and len(args) > 0:
                                                args = (data,) + args[1:]
                                            elif 'data' in kwargs:
                                                kwargs['data'] = data
                                        elif hasattr(data, '_mutable'):
                                            data._mutable = True

                                        data[field_name] = ['[]']
                                else:
                                    # Single value or other malformed data
                                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Processing single value in {field_name}")
                                    if raw_field_data and raw_field_data != '[]':
                                        fixed_json = json.dumps([raw_field_data])
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: FIXING SINGLE VALUE in {field_name}: '{raw_field_data}' -> '{fixed_json}'")

                                        # CRITICAL: Create a new mutable QueryDict if needed
                                        from django.http import QueryDict
                                        if isinstance(data, QueryDict) and not data._mutable:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for string single value")
                                            new_data = data.copy()
                                            new_data._mutable = True
                                            data = new_data
                                            # Update the reference in args or kwargs
                                            if args and len(args) > 0:
                                                args = (data,) + args[1:]
                                            elif 'data' in kwargs:
                                                kwargs['data'] = data
                                        elif hasattr(data, '_mutable'):
                                            data._mutable = True

                                        data[field_name] = [fixed_json]
                                    else:
                                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Empty {field_name} value, setting to empty array")

                                        # CRITICAL: Create a new mutable QueryDict if needed
                                        from django.http import QueryDict
                                        if isinstance(data, QueryDict) and not data._mutable:
                                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Creating new mutable QueryDict for string empty value")
                                            new_data = data.copy()
                                            new_data._mutable = True
                                            data = new_data
                                            # Update the reference in args or kwargs
                                            if args and len(args) > 0:
                                                args = (data,) + args[1:]
                                            elif 'data' in kwargs:
                                                kwargs['data'] = data
                                        elif hasattr(data, '_mutable'):
                                            data._mutable = True

                                        data[field_name] = ['[]']
                        else:
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Skipping {field_name} - unexpected data type: {raw_field_data} (type: {type(raw_field_data)})")

        super(ContentFilterForm, self).__init__(*args, **kwargs)

        # CRITICAL FIX: Set field initial values after form initialization
        # This ensures that unbound forms (GET requests) display saved values correctly
        # The demo template expects comma-separated values, not JSON arrays
        if not self.is_bound and self.initial:
            # Set initial values on the actual form fields for proper template display
            for field_name in ['regions', 'brands', 'brands_exclude']:
                if field_name in self.initial and field_name in self.fields:
                    initial_value = self.initial[field_name]

                    # Convert JSON array to comma-separated string for demo template
                    if isinstance(initial_value, list):
                        if initial_value:  # Non-empty list
                            comma_separated = ','.join(initial_value)
                            self.fields[field_name].initial = comma_separated
                            # Keep form.initial in sync so BoundField.value() reflects the same value
                            self.initial[field_name] = comma_separated
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Set field initial for {field_name}: {initial_value} -> '{comma_separated}'")
                        else:  # Empty list
                            self.fields[field_name].initial = ''
                            self.initial[field_name] = ''
                            logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Set field initial for {field_name}: {initial_value} -> ''")
                    else:
                        # Already string or other format
                        self.fields[field_name].initial = initial_value
                        self.initial[field_name] = initial_value
                        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Set field initial for {field_name}: {initial_value}")

        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: ContentFilterForm.__init__ completed")
        logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: self.data after init: {dict(self.data) if hasattr(self, 'data') and self.data else 'No data'}")

        # Debug field values for template display
        if not self.is_bound:
            for field_name in ['regions', 'brands', 'brands_exclude']:
                if field_name in self.fields:
                    field_value = self[field_name].value()
                    logger.debug(f"🔍 FORM INIT [Thread {thread_id}]: Field {field_name} value(): {field_value} (type: {type(field_value)})")

    def clean_regions(self):
        """
        Custom validation for regions field to handle JSON string conversion.

        The AngularJS TagChoiceCtrl sends regions data as a JSON string (e.g., '["region1", "region2"]'),
        but Django's JSONField expects parsed JSON data. This method converts the JSON string
        to a proper Python list for form validation.
        """
        import json
        import logging
        import threading
        logger = logging.getLogger(__name__)

        thread_id = threading.get_ident()

        regions_data = self.cleaned_data.get('regions')
        logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: clean_regions() called")
        logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: regions_data from cleaned_data: {regions_data} (type: {type(regions_data)})")

        # Also log the raw form data for comparison
        if hasattr(self, 'data') and 'content-regions' in self.data:
            raw_regions = self.data['content-regions']
            logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Raw form data content-regions: {raw_regions} (type: {type(raw_regions)})")

        # If regions_data is already a list, return it as-is
        if isinstance(regions_data, list):
            logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Regions data is already a list: {regions_data}")
            return regions_data

        # If regions_data is a string, try to parse it as JSON
        if isinstance(regions_data, str):
            logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Attempting to parse regions string: '{regions_data}'")
            try:
                parsed_regions = json.loads(regions_data)
                if isinstance(parsed_regions, list):
                    logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Successfully parsed regions JSON string: {parsed_regions}")
                    return parsed_regions
                else:
                    logger.warning(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Parsed regions data is not a list: {parsed_regions}")
                    return []
            except json.JSONDecodeError as e:
                logger.error(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Failed to parse regions JSON string '{regions_data}': {e}")
                return []

        # If regions_data is None or empty, return empty list
        if not regions_data:
            logger.debug(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Regions data is empty, returning empty list")
            return []

        # Fallback: return empty list for any other data type
        logger.warning(f"🔍 CLEAN REGIONS [Thread {thread_id}]: Unexpected regions data type {type(regions_data)}: {regions_data}")
        return []

    def clean_brands(self):
        """
        Custom validation for brands field to handle JSON string conversion.

        Similar to clean_regions, this handles the brands field which also uses TagChoiceCtrl.
        """
        import json
        import logging
        logger = logging.getLogger(__name__)

        brands_data = self.cleaned_data.get('brands')
        logger.debug(f"Raw brands data: {brands_data} (type: {type(brands_data)})")

        # If brands_data is already a list, return it as-is
        if isinstance(brands_data, list):
            return brands_data

        # If brands_data is a string, try to parse it as JSON
        if isinstance(brands_data, str):
            try:
                parsed_brands = json.loads(brands_data)
                if isinstance(parsed_brands, list):
                    return parsed_brands
                else:
                    return []
            except json.JSONDecodeError:
                return []

        # If brands_data is None or empty, return empty list
        if not brands_data:
            return []

        # Fallback: return empty list for any other data type
        return []

    def clean_brands_exclude(self):
        """
        Custom validation for brands_exclude field to handle JSON string conversion.

        Similar to clean_regions, this handles the brands_exclude field.
        """
        import json
        import logging
        logger = logging.getLogger(__name__)

        brands_exclude_data = self.cleaned_data.get('brands_exclude')
        logger.debug(f"Raw brands_exclude data: {brands_exclude_data} (type: {type(brands_exclude_data)})")

        # If brands_exclude_data is already a list, return it as-is
        if isinstance(brands_exclude_data, list):
            return brands_exclude_data

        # If brands_exclude_data is a string, try to parse it as JSON
        if isinstance(brands_exclude_data, str):
            try:
                parsed_brands_exclude = json.loads(brands_exclude_data)
                if isinstance(parsed_brands_exclude, list):
                    return parsed_brands_exclude
                else:
                    return []
            except json.JSONDecodeError:
                return []

        # If brands_exclude_data is None or empty, return empty list
        if not brands_exclude_data:
            return []

        # Fallback: return empty list for any other data type
        return []

    def clean(self):
        """
        Override clean method to handle JSON string conversion for all fields.

        The issue is that Django's JSONField validation happens before our custom
        clean_* methods, so we need to preprocess the raw form data here.
        """
        import json
        import logging
        import threading
        logger = logging.getLogger(__name__)

        thread_id = threading.get_ident()

        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: ContentFilterForm.clean() called")
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Raw form data: {dict(self.data) if hasattr(self, 'data') and self.data else 'No data'}")
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Current errors before clean: {self.errors}")

        # NOTE: This clean() method is called AFTER individual field validation
        # So if there are already errors from WsJsonFormField validation, they will be here
        if self.errors:
            logger.error(f"🔍 FORM CLEAN [Thread {thread_id}]: ERRORS ALREADY EXIST BEFORE CLEAN: {self.errors}")

        # Preprocess JSON fields before Django's field validation
        json_fields = ['regions', 'brands', 'brands_exclude']

        for field_name in json_fields:
            form_field_name = f'content-{field_name}'  # Form field names have 'content-' prefix
            if form_field_name in self.data:
                raw_value = self.data[form_field_name]
                logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Processing {form_field_name}: {raw_value} (type: {type(raw_value)})")

                if isinstance(raw_value, list) and len(raw_value) > 0:
                    raw_string = raw_value[0]  # Django form data comes as lists
                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Raw string for {form_field_name}: '{raw_string}' (type: {type(raw_string)})")

                    # Check if it's already valid JSON
                    if raw_string:
                        # Try to parse as JSON first
                        try:
                            json.loads(raw_string)
                            # If it parses successfully, it's already valid JSON
                            logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: {form_field_name} data is already valid JSON: {raw_string}")
                        except json.JSONDecodeError:
                            # It's not valid JSON, determine the format and fix it
                            logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: NON-JSON {form_field_name} DATA DETECTED: '{raw_string}'")

                            # Handle comma-separated values from demo template (e.g., "usdm,cdm")
                            if ',' in raw_string and not raw_string.startswith('['):
                                # This is comma-separated values from demo template
                                parts = [part.strip() for part in raw_string.split(',') if part.strip()]
                                if parts:
                                    fixed_json = json.dumps(parts)
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: CONVERTING COMMA-SEPARATED {form_field_name}: '{raw_string}' -> '{fixed_json}'")
                                    # Update the form data
                                    self.data = self.data.copy()  # Make it mutable
                                    self.data[form_field_name] = [fixed_json]
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: DATA UPDATED - {form_field_name}: {self.data[form_field_name]}")
                                else:
                                    # No valid parts, set to empty array
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: No valid parts found in comma-separated {form_field_name}, setting to empty array")
                                    self.data = self.data.copy()
                                    self.data[form_field_name] = ['[]']
                            # Handle malformed JSON with mixed comma-separated and arrays (e.g., "[],usdm,cdm")
                            elif ',' in raw_string and '[' in raw_string:
                                # Split by comma and filter out empty/invalid parts
                                parts = [part.strip() for part in raw_string.split(',') if part.strip() and part.strip() != '[]']
                                if parts:
                                    # Create proper JSON array
                                    fixed_json = json.dumps(parts)
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: FIXING MALFORMED JSON {form_field_name}: '{raw_string}' -> '{fixed_json}'")
                                    # Update the form data
                                    self.data = self.data.copy()  # Make it mutable
                                    self.data[form_field_name] = [fixed_json]
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: DATA UPDATED - {form_field_name}: {self.data[form_field_name]}")
                                else:
                                    # No valid parts, set to empty array
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: No valid parts found in malformed {form_field_name}, setting to empty array")
                                    self.data = self.data.copy()
                                    self.data[form_field_name] = ['[]']
                            else:
                                # Single value, wrap in array
                                if raw_string and raw_string != '[]':
                                    fixed_json = json.dumps([raw_string])
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Fixed single {form_field_name} value: '{raw_string}' -> '{fixed_json}'")
                                    self.data = self.data.copy()
                                    self.data[form_field_name] = [fixed_json]
                                else:
                                    # Empty or just "[]", set to empty array
                                    logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Empty {form_field_name} value, setting to empty array")
                                    self.data = self.data.copy()
                                    self.data[form_field_name] = ['[]']

        # Now call the parent clean method
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Calling super().clean()")
        cleaned_data = super().clean()
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Cleaned data: {cleaned_data}")
        logger.debug(f"🔍 FORM CLEAN [Thread {thread_id}]: Form errors after clean: {self.errors}")

        return cleaned_data

    def decompose_to_initial(self):
        import logging
        import threading
        logger = logging.getLogger(__name__)
        thread_id = threading.get_ident()
        
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Starting data decomposition")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Instance exists: {bool(self.instance)}")
        
        # Handle cases where self.instance might be None or missing keys
        if not self.instance:
            default_initial = {
                'regions': [],
                'only_oem': False,
                'by': '',
                'brands': [],
                'brands_exclude': [],
            }
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: No instance, returning defaults: {default_initial}")
            return default_initial

        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Instance type: {type(self.instance)}")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Instance keys: {list(self.instance.keys()) if hasattr(self.instance, 'keys') else 'No keys method'}")

        # With use_default_json=True, self.instance is a DefaultJson object
        # Extract data safely with default values
        try:
            # DefaultJson.get signature is get(key, default=None)
            # so we must use keyword arg to avoid TypeError when default is list
            regions = self.instance.get('regions', default=[])
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Raw regions: {regions} (type: {type(regions)})")
        except (KeyError, TypeError) as e:
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: No regions found: {e}")
            regions = []

        try:
            only_oem = self.instance['only_oem']
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Raw only_oem: {only_oem}")
        except (KeyError, TypeError) as e:
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: No only_oem found: {e}")
            only_oem = False

        # CRITICAL FIX: Sanitize corrupted JSON data
        # The data may have been over-escaped during previous saves, so we need to clean it
        regions = self._sanitize_json_field(regions, 'regions')

        initial = {
            # Always store regions initial as comma-separated string for template compatibility
            'regions': ','.join(regions) if isinstance(regions, list) else (regions or ''),
            'only_oem': only_oem,
        }

        # Safely update with filter data
        try:
            filter_data = self.instance['filter']
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Raw filter_data: {filter_data} (type: {type(filter_data)})")
            
            if filter_data:
                # Add filter fields with defaults and sanitize them
                # CRITICAL FIX: Use .get() with proper DefaultJson syntax
                try:
                    by_value = filter_data.get('by', default='') or ''
                    brands_raw = filter_data.get('brands', default=[]) or []
                    brands_exclude_raw = filter_data.get('brands_exclude', default=[]) or []
                    
                    logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Raw filter values:")
                    logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   by = '{by_value}'")
                    logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   brands = {brands_raw} (type: {type(brands_raw)})")
                    logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   brands_exclude = {brands_exclude_raw} (type: {type(brands_exclude_raw)})")
                    
                    initial['by'] = by_value
                    initial['brands'] = self._sanitize_json_field(brands_raw, 'brands')
                    initial['brands_exclude'] = self._sanitize_json_field(brands_exclude_raw, 'brands_exclude')
                except Exception as filter_access_error:
                    logger.error(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Filter access error: {filter_access_error}")
                    # Set defaults if individual field access fails
                    initial['by'] = ''
                    initial['brands'] = []
                    initial['brands_exclude'] = []
            else:
                logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Filter data is empty/None")
                initial['by'] = ''
                initial['brands'] = []
                initial['brands_exclude'] = []
                
        except (KeyError, TypeError, AttributeError) as e:
            logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Error accessing filter data: {e}")
            # Set defaults if filter data is missing or invalid
            initial['by'] = ''
            initial['brands'] = []
            initial['brands_exclude'] = []

        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]: Final initial data:")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   regions = {initial['regions']}")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   only_oem = {initial['only_oem']}")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   by = '{initial['by']}'")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   brands = {initial['brands']}")
        logger.debug(f"🔍 DECOMPOSE_TO_INITIAL [Thread {thread_id}]:   brands_exclude = {initial['brands_exclude']}")

        return initial

    def _sanitize_json_field(self, field_value, field_name):
        """
        Sanitize JSON field data that may have been corrupted by over-escaping.

        This fixes data corruption issues where JSON arrays were repeatedly escaped,
        resulting in strings like: ['["[\\"[\\\\\\"usdm\\\\\\"", "\\\\\\"cdm\\\\\\"]\\"]", "cdm", "mxndm"]']

        Args:
            field_value: The potentially corrupted field value
            field_name: Name of the field (for logging)

        Returns:
            list: Clean list of values
        """
        import json
        import logging
        import threading
        logger = logging.getLogger(__name__)
        thread_id = threading.get_ident()

        # If it's already a proper list, return it
        if isinstance(field_value, list) and all(isinstance(item, str) for item in field_value):
            # Check if any items look like escaped JSON
            needs_cleaning = any('\\' in item or item.startswith('[') for item in field_value)
            if not needs_cleaning:
                logger.debug(f"🔍 SANITIZE [Thread {thread_id}]: {field_name} is already clean: {field_value}")
                return field_value

        logger.debug(f"🔍 SANITIZE [Thread {thread_id}]: Sanitizing {field_name}: {field_value} (type: {type(field_value)})")

        # Try to extract clean values from corrupted data
        clean_values = []

        if isinstance(field_value, list):
            for item in field_value:
                if isinstance(item, str):
                    # Try to parse nested JSON strings
                    current = item
                    max_iterations = 10  # Prevent infinite loops
                    iteration = 0

                    while iteration < max_iterations:
                        try:
                            # Try to parse as JSON
                            parsed = json.loads(current)
                            if isinstance(parsed, list):
                                # Successfully parsed to a list
                                clean_values.extend(parsed)
                                break
                            elif isinstance(parsed, str):
                                # Still a string, try parsing again
                                current = parsed
                                iteration += 1
                            else:
                                # Not a list or string, add as-is
                                clean_values.append(str(parsed))
                                break
                        except json.JSONDecodeError:
                            # Not valid JSON, treat as a plain string
                            if current and current not in ['[]', '""']:
                                clean_values.append(current)
                            break
                else:
                    # Not a string, add as-is
                    clean_values.append(str(item))
        elif isinstance(field_value, str):
            # Single string value
            if field_value and field_value not in ['[]', '""']:
                try:
                    parsed = json.loads(field_value)
                    if isinstance(parsed, list):
                        clean_values = parsed
                    else:
                        clean_values = [str(parsed)]
                except json.JSONDecodeError:
                    clean_values = [field_value]

        # Remove duplicates while preserving order
        seen = set()
        result = []
        for value in clean_values:
            if value not in seen and value:  # Skip empty values
                seen.add(value)
                result.append(value)

        logger.debug(f"🔍 SANITIZE [Thread {thread_id}]: {field_name} sanitized: {field_value} -> {result}")
        return result

    def compose_to_save(self, data):
        import logging
        import threading
        logger = logging.getLogger(__name__)
        thread_id = threading.get_ident()
        
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Starting data composition")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Input data type: {type(data)}")
        
        # With use_default_json=True, data is a DefaultJson object
        # Use DefaultJson.get() method with proper signature: get(key, default=None)
        
        # Extract and log the form data
        by_value = data.get('by', default='')
        brands_value = data.get('brands', default=[])
        brands_exclude_value = data.get('brands_exclude', default=[])
        regions_value = data.get('regions', default=[])
        only_oem_value = data.get('only_oem', default=False)
        
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Extracted values:")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   by = '{by_value}'")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   brands = {brands_value} (type: {type(brands_value)})")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   brands_exclude = {brands_exclude_value} (type: {type(brands_exclude_value)})")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   regions = {regions_value} (type: {type(regions_value)})")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   only_oem = {only_oem_value}")
        
        # CRITICAL FIX: Ensure all field values are proper lists, not nested JSON
        brands_clean = self._ensure_list_format(brands_value, 'brands')
        brands_exclude_clean = self._ensure_list_format(brands_exclude_value, 'brands_exclude') 
        regions_clean = self._ensure_list_format(regions_value, 'regions')
        
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Cleaned values:")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   brands_clean = {brands_clean}")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   brands_exclude_clean = {brands_exclude_clean}")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   regions_clean = {regions_clean}")
        
        # CRITICAL FIX: Explicitly save all filter fields to ensure they persist
        filter = {
            'by': by_value,
            'brands': brands_clean,
            'brands_exclude': brands_exclude_clean,
        }

        # Get existing filter keys to preserve any additional fields
        existing_filter = {}
        if self.instance:
            try:
                existing_filter = self.instance['filter']
                logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Found existing filter: {existing_filter}")
                
                if hasattr(existing_filter, 'keys'):
                    # Handle DefaultJson object
                    for key in existing_filter.keys():
                        if key not in ['by', 'brands', 'brands_exclude']:  # Skip fields we already set
                            existing_value = data.get(key, default=[])
                            if not isinstance(existing_value, (list, tuple)):
                                existing_value = []
                            filter[key] = existing_value
                            logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Added existing field '{key}' = {existing_value}")
                elif isinstance(existing_filter, dict):
                    # Handle regular dict
                    for key in existing_filter.keys():
                        if key not in ['by', 'brands', 'brands_exclude']:  # Skip fields we already set
                            existing_value = data.get(key, default=[])
                            if not isinstance(existing_value, (list, tuple)):
                                existing_value = []
                            filter[key] = existing_value
                            logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Added existing field '{key}' = {existing_value}")
            except (KeyError, AttributeError, TypeError) as e:
                logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: No existing filter data: {e}")
                # No existing filter data, just use the fields we set above
                pass

        final_data = {
            'only_oem': only_oem_value,
            'regions': regions_clean,
            'filter': filter,
        }

        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]: Final composed data:")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   only_oem = {final_data['only_oem']}")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   regions = {final_data['regions']}")
        logger.debug(f"🔍 COMPOSE_TO_SAVE [Thread {thread_id}]:   filter = {final_data['filter']}")
        
        return final_data

    def _ensure_list_format(self, value, field_name):
        """
        Ensure value is a proper list of strings, not nested JSON or other formats.
        This fixes the issue where demo form saves ["[\"a\", \"b\"]"] instead of ["a", "b"].
        """
        import logging
        import json
        logger = logging.getLogger(__name__)
        
        logger.debug(f"🔍 ENSURE_LIST: Processing {field_name}: {value} (type: {type(value)})")
        
        if not value:
            return []
            
        if isinstance(value, list):
            # Process each item in the list
            result = []
            for item in value:
                if isinstance(item, str):
                    # Try to parse as JSON if it looks like JSON
                    if item.startswith('[') and item.endswith(']'):
                        try:
                            parsed = json.loads(item)
                            if isinstance(parsed, list):
                                result.extend(parsed)
                            else:
                                result.append(str(parsed))
                        except json.JSONDecodeError:
                            # Not valid JSON, treat as string
                            result.append(item)
                    else:
                        # Regular string, add it
                        result.append(item)
                else:
                    # Not a string, convert to string
                    result.append(str(item))
            
            logger.debug(f"🔍 ENSURE_LIST: {field_name} result: {result}")
            return result
        elif isinstance(value, str):
            # Single string value, try to parse as JSON or split by comma
            if value.startswith('[') and value.endswith(']'):
                try:
                    parsed = json.loads(value)
                    if isinstance(parsed, list):
                        return parsed
                    else:
                        return [str(parsed)]
                except json.JSONDecodeError:
                    return [value]
            else:
                return [value]
        else:
            return [str(value)]

    @cached_property
    def choices(self):
        """
        Get choices data for finder-v2 widget configuration.

        Uses FinderV2WidgetProxyView to ensure v2 API endpoints are used,
        which are required for the finder-v2 widget functionality.
        """
        if not self.widget:
            return {
                'brands': [],
                'regions': [],
            }

        brands_url = reverse('widget-api:makes', kwargs={'widget_slug': self.widget.slug})
        regions_url = reverse('widget-api:regions', kwargs={'widget_slug': self.widget.slug})
        
        brands_choices = self.get_choices(brands_url)
        regions_choices = self.get_choices(regions_url)

        # Parse JSON strings to Python objects for template use
        try:
            brands_data = json.loads(brands_choices)
            brands_list = brands_data.get('data', []) if isinstance(brands_data, dict) else []
        except json.JSONDecodeError:
            brands_list = []
            
        try:
            regions_data = json.loads(regions_choices)
            regions_list = regions_data.get('data', []) if isinstance(regions_data, dict) else []
        except json.JSONDecodeError:
            regions_list = []

        return {
            'brands': brands_list,
            'regions': regions_list,
        }

    def get_choices(self, url, json_result=True):
        """
        Get choices data directly from Wheel Fitment API for form initialization.

        This method bypasses the widget API proxy and calls the external Wheel Fitment API
        directly using the REST_PROXY configuration. This avoids CSRF protection issues
        since these are internal form initialization calls, not user-initiated requests.

        Args:
            url (str): Widget API endpoint URL (used to determine the external API endpoint).
            json_result (bool): Whether to return a raw JSON string or parsed data.

        Returns:
            str or list: JSON string or parsed data depending on json_result.
        """
        import requests
        from django.conf import settings

        try:
            # Map widget API endpoints to external API endpoints
            endpoint_mapping = {
                'mk': 'makes',        # /api/mk -> makes API
                'rg': 'regions'       # /api/rg -> regions API
            }
            
            # Extract endpoint type from URL
            endpoint_type = None
            for widget_endpoint, api_endpoint in endpoint_mapping.items():
                if widget_endpoint in url:
                    endpoint_type = api_endpoint
                    break
            
            if not endpoint_type:
                return '[]' if json_result else []

            # Get REST_PROXY configuration
            rest_proxy = getattr(settings, 'REST_PROXY', {})
            api_host = rest_proxy.get('HOST', 'https://api3.wheel-size.com')
            api_headers = rest_proxy.get('HEADERS', {})
            
            # Ensure v2 API endpoint for finder-v2
            if not api_host.endswith('/v2'):
                api_host = api_host.rstrip('/') + '/v2'
            
            # Build the external API URL
            external_url = f"{api_host}/{endpoint_type}/"
            
            # Make direct request to external API
            response = requests.get(
                external_url,
                headers=api_headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.text
            else:
                result = '[]'
                
        except Exception as e:
            # Handle any exceptions during API calls
            result = '[]'

        return result if json_result else json.loads(result)


class FinderV2InterfaceForm(WidgetInterfaceForm):
    TABS = FinderV2InterfaceTabs.TABS
    TAB_CHOICES = FinderV2InterfaceTabs.TAB_CHOICES

    FLOW_TYPE_CHOICES = (
        ('primary', _('Primary Flow (Year → Make → Model → Modifications)')),
        ('alternative', _('Alternative Flow (Make → Model → Generation → Modifications)')),
    )

    # Hidden fields - finder-v2 only has one tab, so these are automatically set
    tabs = forms.MultipleChoiceField(choices=TAB_CHOICES, widget=forms.MultipleHiddenInput())
    primary_tab = forms.ChoiceField(choices=TAB_CHOICES, widget=forms.HiddenInput())
    button_to_ws = forms.BooleanField(required=False,
                                      label='Show "See on Wheel-Size.com" button')
    flow_type = forms.ChoiceField(choices=FLOW_TYPE_CHOICES,
                                  initial='primary',
                                  label=_('API Flow Type'),
                                  help_text=_('Choose the search flow for vehicle selection'))

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Drop the height field to prevent it rendering in the UI
        self.fields.pop('height', None)

    def decompose_to_initial(self):
        # Width comes from stored config; height intentionally omitted
        try:
            width_val = self.instance['dimensions']['width']
        except Exception:
            width_val = 600

        initial = {
            'width': width_val,
            # no height field
        }

        return initial

    def compose_to_save(self, data):
        # Dimensions: only width retained; height forced to blank for auto-resize
        dimensions = {
            'width': data.get('width', '') or '',
            'height': '',
        }

        result = {
            'dimensions': dimensions,
            'flow_type': data['flow_type'],
            'tabs': {
                'visible': ['by_vehicle'],
                'primary': 'by_vehicle',
            },
            'blocks': {
                'button_to_ws': {
                    'hide': not data['button_to_ws'],
                }
            }
        }
        return result

    def clean_primary_tab(self):
        # Finder-v2 only has one tab (by_vehicle), so validation is simplified
        # Always return the single available tab regardless of form input
        return 'by_vehicle'

    def clean_tabs(self):
        # Finder-v2 only has one tab (by_vehicle), so always return it
        # This ensures consistency regardless of form input
        return ['by_vehicle']


class FinderV2ThemeForm(FakeModelForm):
    """
    Simplified theme form for Finder-v2 widget.

    Since finder-v2 uses Vue 3 + TailwindCSS instead of LESS, we don't need
    the complex theme compilation logic from the base ThemeForm.
    """
    theme_name = forms.ChoiceField(choices=[])

    use_default_json = False

    def __init__(self, *args, **kwargs):
        super(FinderV2ThemeForm, self).__init__(*args, **kwargs)

        # Set theme choices from FinderV2Themes
        theme_choices = [(name, theme['label']) for name, theme in FinderV2Themes.THEMES.items()]
        self.fields['theme_name'].choices = theme_choices

    def decompose_to_initial(self):
        try:
            active_theme = self.instance.get('active', {})
            theme_name = active_theme.get('theme_name', FinderV2Themes.default_theme)
        except (KeyError, AttributeError):
            theme_name = FinderV2Themes.default_theme

        return {
            'theme_name': theme_name,
        }

    def compose_to_save(self, data):
        theme_name = data['theme_name']

        # Get the selected theme from FinderV2Themes
        if theme_name in FinderV2Themes.THEMES:
            theme = FinderV2Themes.THEMES[theme_name].copy()
        else:
            # Fallback to default theme
            theme = FinderV2Themes.THEMES[FinderV2Themes.default_theme].copy()

        return {
            'active': theme,
            'inactive': None,
        }


class FinderV2ConfigForm(WidgetConfigForm):
    form_classes = {
        'config': WidgetCommonForm,
        'theme': FinderV2ThemeForm,
        'content': ContentFilterForm,
        'interface': FinderV2InterfaceForm,
        'permissions': WidgetPermissionsForm,
    }


class FinderV2DemoConfigForm(WidgetConfigForm):
    form_classes = {
        'config': DemoWidgetCommonForm,
        'theme': FinderV2ThemeForm,
        'content': ContentFilterForm,
        'interface': FinderV2InterfaceForm,
    }



