from src.apps.widgets.finder_v2.default_config.themes import FinderV2Themes


FINDER_V2_DEFAULT_CONFIG = {
    "interface": {
        "dimensions": {
            "width": 600,
            "height": ""
        },
        "flow_type": "primary",  # New: 'primary' (Year→Make→Model) or 'alternative' (Make→Model→Generation)
        "api_version": "v2",     # New: API version for this widget type
        "tabs": {
            "visible": [
                "by_vehicle"
            ],
            "primary": "by_vehicle"
        },
        "blocks": {
            "button_to_ws": {
                # Will be hidden only if subscription is paid
                "hide": True,
            }
        },
    },
    "permissions": {
        "domains": [
            "localhost",
            "development.local",
            "127.0.0.1",
            "*.localhost",
            "*.development.local"
        ]
    },
    "content": {
        "regions": [],
        "filter": {
            "brands": [],
            "countries": [],
            "brands_exclude": [],
            "countries_exclude": [],
            "by": None
        },
        "only_oem": False
    },
    "theme": {
        "active": FinderV2Themes.get_default(),
        "inactive": None,
    }
}
