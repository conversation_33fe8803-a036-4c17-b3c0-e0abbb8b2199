"""
Docker development settings without WS packages
Temporary settings for testing Docker setup without WS package dependencies
"""

from .base import *
from .env import get_env_variable, get_env_int

# Database configuration for Docker
DATABASES = {
    'default': {
        'ENGINE': get_env_variable('DB_ENGINE', default='django.db.backends.postgresql_psycopg2', required=False),
        'NAME': get_env_variable('DB_NAME', default='ws_services_db', required=False),
        'USER': get_env_variable('DB_USER', default='ws_services_user', required=False),
        'PASSWORD': get_env_variable('DB_PASSWORD', default='89JXdCdNUEMKCRLu', required=False),
        'HOST': get_env_variable('DB_HOST', default='postgres15', required=False),
        'PORT': get_env_int('DB_PORT', default=5432),
    }
}

# Debug settings
DEBUG = True
ALLOWED_HOSTS = ['*', 'development.local', 'localhost', '127.0.0.1']

# Override INSTALLED_APPS completely for Docker testing (no WS packages)
INSTALLED_APPS = [
    # Django Admin Interface (must be before django.contrib.admin)
    'admin_interface',
    'colorfield',

    # Core Django apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.staticfiles',
    'django.contrib.contenttypes',
    'django.contrib.messages',
    'django.contrib.sessions',
    'django.contrib.sites',

    # Basic apps that don't depend on WS packages
    'maintenance_mode',
    'django_recaptcha',
]

# Use standard Django authentication for now (portal app requires WS packages)
# Portal app will be re-enabled after WS packages are installed

# Override MIDDLEWARE to remove WS-dependent middleware
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'maintenance_mode.middleware.MaintenanceModeMiddleware',
]

# Disable WS-dependent URL patterns
ROOT_URLCONF = 'src.urls_minimal'

# Basic logging for Docker
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}
