{% load i18n %}
{% load form_ext %}


    <!--Show only OE wheels-->
    <div class="border-b border-gray-200 my-5">

      <div class="my-5 ">
        <h3 class="text-base font-semibold text-gray-900">{% trans 'Modification Filtering' %}</h3>
        <p class="mt-2 max-w-4xl text-sm text-gray-500">
          {% blocktrans %}
          When checked, the search results will only include factory-standard 
          vehicle data, hiding aftermarket options.
          {% endblocktrans %}
          <br>
          {% blocktrans %}
          Enable this to ensure search results only contain original equipment from the manufacturer.
        {% endblocktrans %}
        </p>
      </div>



        <div class="checkbox my-5">
          <label>
            {{ form.content.only_oem }} {{ form.content.only_oem.label }}
          </label>
        </div>

    </div>  

    <!-- Hidden fields for tab configuration - finder-v2 only has one tab (by_vehicle) -->
    <!-- These fields are kept for form validation but hidden from UI since tab selection is not needed -->
    <div style="display: none;">
      {{ form.interface.tabs }}
      {{ form.interface.primary_tab }}
    </div>


    <!--API Flow Type-->
    <div class="border-b border-gray-200 my-5 max-w-4xl">
      <div class="form-group {% if form.interface.errors.flow_type %} has-error{% endif %}">
        <label class="control-label"></label>
        <h3 class="text-base font-semibold text-gray-900">
          User Search Path
          
          
        </h3>
        {% if form.interface.flow_type.help_text %}
        <p class="my-2 max-w-4xl text-sm text-gray-500 help-block">
          <!--{{ form.interface.flow_type.help_text }}-->
          Select the order of dropdowns users will see when searching for a vehicle.
        </p>
        {% endif %}
        <div>
          <label for="location" class="block text-sm/6 font-medium text-gray-900">{{ form.interface.flow_type.label }}</label>
          <div class="mt-2 grid grid-cols-1">
                {{ form.interface.flow_type|htmlclass:'col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pr-8 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6' }}
            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        
        <!-- {{ form.interface.flow_type|htmlclass:'form-control' }} -->

        {% if form.interface.errors.flow_type %}
          <span class="text-danger">{{ form.interface.errors.flow_type.as_text }}</span>
        {% endif %}
      </div>
    </div>

    {% if object.subscription.is_paid %}
      <div>
        <p class="note">
          You have a paid widget version.
        </p>

        <div class="checkbox">
          <label>
            {{ form.interface.button_to_ws }} {{ form.interface.button_to_ws.label }}
          </label>
        </div>
      </div>
    {% endif %}


