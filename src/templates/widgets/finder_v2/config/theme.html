{% load i18n %}

<div class="row">
  <div class="col-md-12">
    <h3><span>{% trans 'Theme Selection' %}</span></h3>
  </div>
</div>

<div class="row">
  {% if form.theme.non_field_errors %}
    <div class="col-md-12">
      <div class="alert alert-danger">
        <pre style="margin: 0;">{{ form.theme.non_field_errors.as_text }}</pre>
      </div>
    </div>
  {% endif %}

  <div class="col-md-12">
    <p class="note">
      {% blocktrans %}
        Finder V2 uses modern Vue 3 + TailwindCSS styling. Choose from available themes below.
      {% endblocktrans %}
    </p>
  </div>

  <div class="col-md-12 form-group">
    <label class="control-label">{% trans 'Choose Theme' %}</label>
    <div>
      <div class="btn-group" data-toggle="buttons">
        {% for choice in form.theme.theme_name.field.choices %}
          <label class="btn btn-default {% if choice.0 == form.theme.theme_name.value %}active{% endif %}">
            <input type="radio"
                   name="theme-theme_name"
                   value="{{ choice.0 }}"
                   autocomplete="off"
                   {% if choice.0 == form.theme.theme_name.value %}checked{% endif %}>
            <span>{{ choice.1 }}</span>
          </label>
        {% endfor %}
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Handle theme selection with plain JavaScript
    const themeRadios = document.querySelectorAll('input[name="theme-theme_name"]');
    const themeLabels = document.querySelectorAll('.btn-group label');
    
    themeRadios.forEach(function(radio) {
      radio.addEventListener('change', function() {
        // Update button states
        themeLabels.forEach(function(label) {
          label.classList.remove('active');
        });
        
        if (this.checked) {
          this.parentElement.classList.add('active');
        }
      });
    });
  });
</script>
