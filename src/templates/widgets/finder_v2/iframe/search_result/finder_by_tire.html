{% verbatim %}
<div class="finder finder-by-tire brand-icons"
     ng-controller="FinderCtrl"
     name="byTire"
     ng-init="finder.loadNext()">
  <form class="form-inline">
    <h4>{{ 'Specify values for fields below to find matching vehicles:'|i18n }}</h4>

    <select selectpicker="{dropupAuto:false}"
            ng-repeat="resource in finder.chain"
            ng-model="resource.value"
            name="{{ resource.name }}"
            ng-change="finder.changed($index)"
            ng-disabled="resource.disabled">
      <option value="">{{ resource.title|i18n }}</option>
      <option value="{{ option.slug }}" ng-repeat="option in resource.items">{{ option.name }}</option>
    </select>
  </form>

  <div class="finder-result-loading" ng-show="finder.loading">
    <span class="loader"></span>
  </div>

  <div class="finder-result" ng-show="finder.result.items.length">
    <p>{{ 'Wheels with these parameters may fit the following vehicles:'|i18n }}</p>

    <div class="matching-make" ng-repeat="item in finder.result.items">
      <div class="make-{{ item.make.slug }} tag">
        <a class="text" ng-href="{{ buildUrl(item.make.slug) }}" target="_blank">{{ item.make.name }}</a>
        <span>({{ item.models.length }})</span>
      </div>
      <div class="models-list">
        <a href=""
           ng-click="item.make.selectedModel = (item.make.selectedModel == model) ? null : model"
           class="matching-model"
           ng-class="{selected: model == item.make.selectedModel}"
           ng-repeat="model in item.models">{{ model.name }} <span>({{ model.start_year }} - {{ model.end_year }})</span>
        </a>
      </div>

      <div class="matching-trims-years" ng-if="item.make.selectedModel">
        <h4>
          {{ 'Trims and Years for'|i18n }}
          <a ng-href="{{ buildUrl(item.make.slug, item.make.selectedModel.slug) }}" target="_blank">
            {{ item.make.name }} {{ item.make.selectedModel.name }}
          </a>
        </h4>
        <div class="trim-years" ng-repeat="vehicle in item.make.selectedModel.vehicles">
          <span class="trim">{{ vehicle.trim }}</span>
          <a ng-repeat="year in vehicle.years" class="year" target="_blank"
             ng-href="{{ buildUrl(item.make.slug, item.make.selectedModel.slug, year, vehicle) }}">{{ year }}</a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endverbatim %}