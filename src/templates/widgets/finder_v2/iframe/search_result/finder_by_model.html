{% verbatim %}
<div class="finder finder-by-model brand-icons"
     ng-controller="FinderCtrl"
     name="byModel"
     ng-init="finder.loadNext()">
  <form class="form-inline">
    <h4>{{ 'Specify make, year and model to find matching wheels:'|i18n }}</h4>

    <select selectpicker="{dropupAuto:false}"
            ng-repeat="resource in finder.chain"
            ng-model="resource.value"
            name="{{ resource.name }}"
            ng-change="finder.changed($index)"
            ng-disabled="resource.disabled">
      <option value="">{{ resource.title|i18n }}</option>
      <option value="{{ option.slug }}" class="{{ resource.name }}-{{ option.slug }}" ng-repeat="option in resource.items">{{ option.name }}</option>
    </select>
  </form>

  <div class="finder-result-loading" ng-show="finder.loading">
    <span class="loader"></span>
  </div>

  <div class="finder-result adaptive-tables" ng-if="finder.result.items.length">
    <p>{{ 'Please, note:'|i18n }} <b>{{ 'highlighted'|i18n }}</b> {{ 'items are for OEM wheels'|i18n }}</p>

    <table class="table table-condenced"
           ng-repeat="group in finder.result.items"
           ng-class="{ 'ws-collapse': !group.expand }">
      <thead class="market-header"
             ng-click="group.expand = !group.expand;"
             ng-if="finder.result.items.length > 1">
        <tr>
          <td colspan="6">
            <span class="market-name">{{ group.market.name|i18n }}</span>
            <span class="glyphicon glyphicon-menu-down"></span>
          </td>
        </tr>
      </thead>
      <thead class="content-header">
        <tr>
          <th>{{ 'Trim'|i18n }}</th>
          <th>{{ 'Tire'|i18n }}</th>
          <th>{{ 'Rim'|i18n }}</th>
          <th>{{ 'Bolt Pattern'|i18n }}</th>
          <th>{{ 'THD'|i18n }}</th>
          <th>{{ 'CB'|i18n }}</th>
        </tr>
      </thead>

      <tbody>
        <tr ng-repeat-start="vehicle in group.vehicles" class="hide fake-row">
        <tr ng-repeat="wheel in vehicle.wheels"
            ng-class="{ oem: wheel.is_stock,
                        collapse: $index > 0 && !wheel.is_stock }">
          <td>
            <span ng-if="$index === 0">
              <ws-user-link place="beforeTrim"></ws-user-link>
              <ws-user-link place="replaceTrim">{{ vehicle.trim }}</ws-user-link>
              <ws-user-link place="afterTrim"></ws-user-link>
              <a class="pull-right caret caret-collapsed"
                 ng-if="$index === 0 &&
                        vehicle.wheels.length > 1 &&
                        !vehicle.wheels[vehicle.wheels.length - 1].is_stock"
                 href="#" finder-collapse>
              </a>
            </span>
          </td>
          <td>
            <ws-user-link place="beforeTire"></ws-user-link>
            <ws-user-link place="replaceTire" side="front">{{ wheel.front.tire }}</ws-user-link>
            <span ng-if="wheel.rear.tire">/</span>
            <ws-user-link place="replaceTire" side="rear">{{ wheel.rear.tire }}</ws-user-link>
            <ws-user-link place="afterTire"></ws-user-link>
          </td>
          <td>
            <ws-user-link place="beforeRim"></ws-user-link>
            <ws-user-link place="replaceRim" side="front">{{ wheel.front.rim }}</ws-user-link>
            <span ng-if="wheel.rear.rim">/</span>
            <ws-user-link place="replaceRim" side="rear">{{ wheel.rear.rim }}</ws-user-link>
            <ws-user-link place="afterRim"></ws-user-link>
          </td>
          <td>
            <span ng-if="$index === 0">
              <ws-user-link place="beforeBoltPattern"></ws-user-link>
              <ws-user-link place="replaceBoltPattern">{{ vehicle.bolt_pattern }}</ws-user-link>
              <ws-user-link place="afterBoltPattern"></ws-user-link>
            </span>
          </td>
          <td>
            <span ng-if="$index === 0">
              <ws-user-link place="beforeThd"></ws-user-link>
              <ws-user-link place="replaceThd">{{ vehicle.lock_text }}</ws-user-link>
              <ws-user-link place="afterThd"></ws-user-link>
            </span>
          </td>
          <td>
            <span ng-if="$index === 0">
              <ws-user-link place="beforeCb"></ws-user-link>
              <ws-user-link place="replaceCb">{{ vehicle.centre_bore }}</ws-user-link>
              <ws-user-link place="afterCb"></ws-user-link>
            </span></td>
        </tr>
        <tr ng-repeat-end class="hide fake-row">
      </tbody>
    </table>


    <ws-user-link place="beforeButton"></ws-user-link>
{% endverbatim %}
    {% if config.subscription.is_paid and blocks.button_to_ws.hide %}
      {# show nothing #}
    {% else %}
{% verbatim %}
      <a class="btn btn-go-to-ws" ng-href="{{ finder.result.info.urlToWs + utm }}" target="_blank">
        <span class="model-title">{{ finder.result.info.title }}</span>
        <span class="separator">{{ 'on'|i18n }}</span>
        <span class="ws-logo">
          <span class="ws-wheel">Wheel-</span><span class="ws-size">Size.com</span>
        </span>
        <span class="glyphicon glyphicon-hand-right"></span>
      </a>
{% endverbatim %}
    {% endif %}
    <ws-user-link place="afterButton"></ws-user-link>
  </div>
</div>
