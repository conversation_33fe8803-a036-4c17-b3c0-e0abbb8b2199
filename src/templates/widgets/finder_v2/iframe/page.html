{% load static %}
{% load i18n %}
{% load jsonify %}

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>{% trans 'WheelSize Widget' %} - Finder V2</title>
  <base href="/">

  <!--[if lt IE 9]>
    <script src="{% static 'widget/js/ie/ie8.js' %}"></script>
  <![endif]-->

  <!--[if lt IE 10]>
    <script src="{% static 'widget/js/ie/es5-shim.min.js' %}"></script>
    <script src="{% static 'widget/js/ie/base64.min.js' %}"></script>
  <![endif]-->

  <!-- Finder V2 CSS -->
  <link rel="stylesheet" href="{% static config.widget_type.static.app_css %}"/>

  <!-- Basic loading styles -->
  <style>
    .finder-v2-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 200px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      color: #374151;
      background: #f9fafb;
      border-radius: 8px;
      margin: 10px;
    }

    .finder-v2-loading h3 {
      margin: 0 0 10px 0;
      font-size: 18px;
      font-weight: 600;
    }

    .finder-v2-loading p {
      margin: 5px 0;
      font-size: 14px;
      color: #6b7280;
    }

    .finder-v2-spinner {
      width: 24px;
      height: 24px;
      border: 3px solid #e5e7eb;
      border-top: 3px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .finder-v2-error {
      color: #dc2626;
      background: #fef2f2;
      border: 1px solid #fecaca;
      padding: 15px;
      border-radius: 6px;
      margin: 10px;
    }
  </style>

  <!-- iFrame Resizer for proper height management -->
  <script src="{% static 'finder_v2/js/libs/iframeResizer.contentWindow.min.js' %}"></script>

  <!-- iFrame Resizer for proper height management -->
  <script src="{% static 'finder_v2/js/libs/iframeResizer.contentWindow.min.js' %}"></script>

  <!-- Finder V2 JavaScript -->
  <script type="module" src="{% static config.widget_type.static.app_js_libs %}?v={% now 'YmdHis' %}&hash=abc123"></script>
  <script type="module" src="{% static config.widget_type.static.app_js %}?v={% now 'YmdHis' %}&hash=abc123"></script>

  <!-- Widget Configuration -->
  <script>
    // Basic widget configuration for Vue 3 app
    window.wsConfig = {
      translation: {}
    };

    // Vue 3 app configuration for finder-v2 widget
    window.FinderV2Config = {
      configMode: {{ page_options.config_mode|yesno:"true,false" }},
      widgetUuid: '{{ config.uuid }}',
      widgetType: '{{ config.type }}',
      flowType: '{{ config.params.interface.flow_type|default:"primary" }}',
      apiVersion: '{{ config.params.interface.api_version|default:"v2" }}',
      tabs: {
        visible: {{ config.params.interface.tabs.visible|jsonify|safe }},
        primary: '{{ config.params.interface.tabs.primary|default:"by_vehicle" }}'
      },
      content: {
        regions: {{ config.params.content.regions|default:"[]"|jsonify|safe }},
        filter: {{ config.params.content.filter|jsonify|safe }},
        onlyOem: {{ config.params.content.only_oem|default:"false"|yesno:"true,false" }}
      },
      permissions: {
        domains: {{ config.params.permissions.domains|jsonify|safe }}
      },
      csrfToken: '{% load widget_csrf %}{% widget_csrf_token request %}',
      utm: '?utm_campaign=widget&utm_medium=web&utm_source={{ config.slug }}',
      slug: '{{ config.slug }}',
      iframeResize: true,
      // Dimensions are informative only; height is left blank to allow automatic resizing via iframeResizer
      dimensions: {
        width: {{ config.params.interface.dimensions.width|default:"600" }},
        height: ''
      },
      widgetResources: {
        // Vehicle search API endpoints only - finder-v2 supports only by_vehicle tab
        make: ['Make', '{% url "widget-api:makes" config.slug %}'],
        model: ['Model', '{% url "widget-api:models" config.slug %}'],
        year: ['Year', '{% url "widget-api:years" config.slug %}'],
        modification: ['Modification', '{% url "widget-api:modifications" config.slug %}'],
        generation: ['Generation', '{% url "widget-api:generations" config.slug %}'],
        search_by_model: ['', '{% url "widget-api:search-by-model" config.slug %}']
      },
      // Base URL for API requests (includes port for development)
      baseUrl: '{{ request.scheme }}://{{ request.get_host }}'
    };

    // Configuration loaded for Vue 3 app
  </script>
</head>
<body>
  <!-- Vue 3 app will mount here -->
  <div id="finder-v2-app">
    <div class="finder-v2-loading">
      <div class="finder-v2-spinner"></div>
      <h3>Loading Finder V2 Widget</h3>
      <p>Initializing Vue 3 application...</p>
      <p>Config: {{ config.uuid }}</p>
    </div>
  </div>

  <script>
    // Initialize Vue 3 app when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
      // Add timeout to show error if Vue app doesn't load
      setTimeout(function() {
        var loadingElement = document.querySelector('.finder-v2-loading');
        if (loadingElement && loadingElement.style.display !== 'none') {
          loadingElement.innerHTML = '<div class="finder-v2-error">' +
            '<h3>Widget Loading Error</h3>' +
            '<p>The Vue 3 application failed to initialize. Please check the console for errors.</p>' +
            '<p>Config UUID: {{ config.uuid }}</p>' +
            (window.FinderV2Config && window.FinderV2Config.configMode ? '<p>Mode: Config</p>' : '<p>Mode: Standard</p>') +
            '</div>';
        }
      }, 10000); // 10 second timeout
    });
  </script>
</body>
</html>
