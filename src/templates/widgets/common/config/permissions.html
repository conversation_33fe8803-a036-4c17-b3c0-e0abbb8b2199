{% load i18n %}
{% load form_ext %}

<div class="row">
  <div class="col-md-12">
    <h3><span>{% trans 'Specify Domain Names' %}</span></h3>

    <p class="note">
      {% blocktrans %}
        Please, specify all domain names where you want to deploy widget with this configuration.
      {% endblocktrans %}
    </p>

    <p class="note">
      {% blocktrans %}
        Every present domain will match this domain and all its subdomains.<br/>
        Example: <br/>
        <b>wheel-size.com</b> will work at <span class="text-success">wheel-size.com</span>, <span class="text-success">www.wheel-size.com</span>, <span class="text-success">services.wheel-size.com</span> <br/>
        <b>*.wheel-size.com</b> provides the same behaviour.
      {% endblocktrans %}
    </p>
  </div>
  
  <div class="form-group col-md-6">
    <label class="control-label">{% trans 'Authorized Domains' %}</label>
    {% if form.permissions.errors.domains %}
      <div class="text-danger mb20">{{ form.permissions.errors.domains.as_text }}</div>
    {% endif %}

    <!-- Simple textarea for domains input (vanilla JS compatible) -->
    <!-- The actual form field (hidden) -->
    {{ form.permissions.domains|htmlclass:"hide" }}

    <!-- User-friendly textarea for editing -->
    <textarea class="form-control domains-textarea"
              rows="6"
              placeholder="{% trans 'Enter one domain per line, e.g.:' %}&#10;example.com&#10;*.example.com&#10;subdomain.example.com"></textarea>
    
    <p class="help-block">
      {% trans 'Enter one domain per line. Use * for wildcard subdomains.' %}
    </p>
  </div>
</div>

<script>
  // Simple vanilla JavaScript for domain validation (no AngularJS required)
  document.addEventListener('DOMContentLoaded', function() {
    const hiddenField = document.querySelector('input[name="permissions-domains"]');
    const userTextarea = document.querySelector('textarea.domains-textarea');

    if (hiddenField && userTextarea) {
      // Initialize user textarea with values from hidden field
      initializeTextareaFromHiddenField();

      // Update hidden field when user textarea changes
      userTextarea.addEventListener('input', updateHiddenFieldFromTextarea);
      userTextarea.addEventListener('blur', validateDomains);

      // Update hidden field before form submission
      const form = hiddenField.closest('form');
      if (form) {
        form.addEventListener('submit', updateHiddenFieldFromTextarea);
      }
    }

    function initializeTextareaFromHiddenField() {
      try {
        let currentValue = hiddenField.value;
        if (currentValue) {
          // Try to parse as JSON array
          if (typeof currentValue === 'string') {
            if (currentValue.startsWith('[') && currentValue.endsWith(']')) {
              const domainsArray = JSON.parse(currentValue);
              if (Array.isArray(domainsArray)) {
                userTextarea.value = domainsArray.join('\n');
                return;
              }
            }
          } else if (Array.isArray(currentValue)) {
            userTextarea.value = currentValue.join('\n');
            return;
          }
        }
        // If no valid value, leave textarea empty
        userTextarea.value = '';
      } catch (e) {
        console.warn('Could not parse domains from hidden field:', e);
        userTextarea.value = '';
      }
    }

    function updateHiddenFieldFromTextarea() {
      const lines = userTextarea.value.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);
      hiddenField.value = JSON.stringify(lines);
    }

    function validateDomains() {
      const lines = userTextarea.value.split('\n');
      const invalidDomains = [];

      lines.forEach(function(line, index) {
        const domain = line.trim();
        if (domain && !isValidDomain(domain)) {
          invalidDomains.push('Line ' + (index + 1) + ': ' + domain);
        }
      });

      // Remove any existing validation messages
      const existingError = userTextarea.parentElement.querySelector('.domain-validation-error');
      if (existingError) {
        existingError.remove();
      }

      // Show validation errors if any
      if (invalidDomains.length > 0) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'domain-validation-error text-warning mt-2';
        errorDiv.innerHTML = '<small><strong>{% trans "Warning:" %}</strong> ' +
          '{% trans "Invalid domain format detected:" %}<br/>' +
          invalidDomains.join('<br/>') + '</small>';
        userTextarea.parentElement.appendChild(errorDiv);
      }
    }
  });
  
  function isValidDomain(domain) {
    // Basic domain validation (allows wildcards)
    if (!domain || domain.length === 0) return false;
    
    // Allow wildcard domains
    if (domain.startsWith('*.')) {
      domain = domain.substring(2);
    }
    
    // Basic domain pattern check
    const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return domainPattern.test(domain);
  }
</script>
