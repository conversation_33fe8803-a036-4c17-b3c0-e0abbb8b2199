[tool.poetry]
name = "services.wheel-size.com"
version = "0.2.0"
description = "Wheel Size Services - Django 4.2 LTS with upgraded WS packages"
authors = ["<PERSON> <<EMAIL>>"]
package-mode = false

[[tool.poetry.source]]
name = "ws"
url = "https://pypi.wheel-size.com/simple/"

[tool.poetry.dependencies]
python = "^3.9"
gunicorn = "^21.2.0"
nodeenv = "^1.8.0"
psycopg2-binary = "^2.9.7"
# pylibmc = "^1.6.3"  # Temporarily disabled - requires libmemcached system dependency
sentry-sdk = "^1.32.0"

django = "^4.2.21"
django-ace = "^1.0.7"
django-autoslug = "^1.9.7"
ws-django-betterforms = "^2.0.0"  # WS-maintained Django 4.2 compatible fork (published to private PyPI)
django-admin-interface = "^0.30.0"
django-maintenance-mode = "^0.14.0"
django-registration-redux = "^2.13"
django-multiselectfield = "^0.1.12"
django-ses = "^1.0.1"
django-storages = "^1.9.1"
django-recaptcha = "^4.1.0"


boto3 = "^1.28.0"
jsonfield = "^3.1.0"
Pillow = "^10.0.0"
sh = "^1.13.1"
tqdm = "^4.47.0"
urllib3 = "^1.25.9"
uuid = "^1.30"
whitenoise = "^6.5.0"

# Updated to Django 4.2+ compatible versions (published to private PyPI)
ws-django-helpers = "^2.0.0"  # WS-maintained Django 4.2 compatible version (published to private PyPI)
ws-django-fields = "^2.0.0"  # WS-maintained Django 4.2 compatible version (published to private PyPI)
ws-django-live-settings = "^2.0.0"  # WS-maintained Django 4.2 compatible version (published to private PyPI)
ws-django-rest-framework-proxy = "^2.0.1"  # WS-maintained Django 4.2 compatible version with HEADERS bug fix (published to private PyPI)
ws-django-tire-calc = "2.0.0"  # WS-maintained Django 4.2 compatible version with JavaScript fixes (published to private PyPI)

[tool.poetry.group.dev.dependencies]
django-extensions = "^3.2.0"
pyquery = "^2.0.0"
selenium = "^4.15.0"
robber = "^1.1.5"
pytest = "^7.4.0"
pytest-django = "^4.5.2"
black = "^23.7.0"
flake8 = "^6.0.0"
lxml = "^4.9.0"
# Node.js environment for TailwindCSS build process
nodeenv = "^1.8.0"

[build-system]
requires = ["poetry>=0.12"]
build-backend = "poetry.masonry.api"
