# Docker Environment Guide

## Overview

The wheel-size-services project uses Docker for containerized development and deployment. This guide explains how the Docker environment is configured and how it differs from local development.

## Docker Configuration

### Main Components

- **Base Image**: Amazon Linux (latest)
- **Python**: 3.12.0 via pyenv
- **Package Manager**: Poetry
- **Database**: PostgreSQL 15 (external container)
- **Web Server**: Django development server (containerized)
- **Reverse Proxy**: Nginx (containerized)

### Key Files

- `docker/Dockerfile` - Main Docker image definition
- `docker-compose.yml` - Multi-container orchestration
- `pyproject.toml` - **Consolidated Poetry configuration** (used by both local and Docker)
- `docker/nginx/services.wheelsize.nginx.conf` - Nginx configuration

## Poetry Configuration Consolidation

### ✅ **CONSOLIDATED APPROACH (Current)**

**Single Configuration File**: `pyproject.toml`
- Used by both local development and Docker environments
- Eliminates version drift between environments
- Simplifies maintenance and ensures development/production parity

**Benefits:**
- ✅ Single source of truth for dependencies
- ✅ No version mismatches between environments
- ✅ Easier maintenance and updates
- ✅ Guaranteed development/production parity

### 🗑️ **DEPRECATED: Docker-Specific File**

**Previous Approach**: `docker/pyproject.docker.toml` (removed)
- Created version drift issues
- Required manual synchronization
- Led to missing bug fixes in Docker environment

## Environment Differences

### Local Development
- **Python**: System Python or pyenv-managed
- **Dependencies**: Installed via Poetry in virtual environment
- **Database**: External PostgreSQL container
- **Static Files**: Served by Django development server
- **Host**: `development.local:8000`

### Docker Environment
- **Python**: pyenv-managed Python 3.12.0 in container
- **Dependencies**: Installed via Poetry (no virtual environment)
- **Database**: External PostgreSQL container (shared)
- **Static Files**: Collected and served by Nginx
- **Host**: `development.local:8000` (proxied through Nginx)

## Docker Build Process

### 1. Base System Setup
```dockerfile
FROM public.ecr.aws/amazonlinux/amazonlinux:latest
# Install system dependencies (gcc, postgresql-devel, etc.)
# Install Node.js for LESS compilation
# Install pyenv for Python version management
```

### 2. Python Environment
```dockerfile
# Install Python 3.12.0 via pyenv
# Set as global Python version
# Upgrade pip to handle modern pyproject.toml
```

### 3. Dependency Installation
```dockerfile
# Install Poetry
# Configure private PyPI repository access
# Copy main pyproject.toml (consolidated configuration)
# Install all dependencies via Poetry
```

### 4. Additional Dependencies
```dockerfile
# Install Django 4.2 compatibility packages via pip
# These are not in Poetry due to version constraints
```

## Private PyPI Configuration

### Repository Setup
```bash
poetry config repositories.ws https://pypi.wheel-size.com/
poetry config http-basic.ws repo-user Ojc3ZSPwIBEEisX
```

### WS Packages (from Private PyPI)
- `ws-django-helpers = "^2.0.0"`
- `ws-django-fields = "^2.0.0"`
- `ws-django-live-settings = "^2.0.0"`
- `ws-django-rest-framework-proxy = "^2.0.1"`
- `ws-django-tire-calc = "^2.0.2"`

## Running Docker Environment

### Start Services
```bash
docker-compose up -d
```

### Check Status
```bash
docker ps
docker-compose logs web
```

### Access Application
- **Web Interface**: http://development.local:8000
- **Admin Interface**: http://development.local:8000/admin/
- **Widget Test Pages**: 
  - http://development.local:8000/widget/finder/try/
  - http://development.local:8000/widget/calc/try/

### Stop Services
```bash
docker-compose down
```

## Troubleshooting

### Common Issues

#### 1. **Build Failures**
```bash
# Clear Docker cache and rebuild
docker-compose down
docker system prune -f
docker-compose build --no-cache
docker-compose up -d
```

#### 2. **Dependency Issues**
```bash
# Check Poetry installation in container
docker exec -it ws_services /root/.pyenv/versions/3.12.0/bin/poetry show

# Verify WS packages are installed
docker exec -it ws_services /root/.pyenv/versions/3.12.0/bin/poetry show | grep ws-django
```

#### 3. **Database Connection Issues**
```bash
# Check PostgreSQL container
docker ps | grep postgres15
docker logs postgres15

# Test database connection from container
docker exec -it ws_services psql -h postgres15 -U ws_services_user -d ws_services_db
```

#### 4. **Static Files Issues**
```bash
# Collect static files manually
docker exec -it ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py collectstatic --noinput
```

### Debug Commands

#### Container Shell Access
```bash
docker exec -it ws_services bash
```

#### Check Python Environment
```bash
docker exec -it ws_services /root/.pyenv/versions/3.12.0/bin/python --version
docker exec -it ws_services /root/.pyenv/versions/3.12.0/bin/poetry --version
```

#### Check Django Configuration
```bash
docker exec -it ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py check
docker exec -it ws_services /root/.pyenv/versions/3.12.0/bin/python manage.py showmigrations
```

## Development Workflow

### Making Changes

1. **Code Changes**: Edit files locally (mounted as volume)
2. **Dependency Changes**: Update `pyproject.toml` and rebuild container
3. **Static Files**: Restart container to collect new static files
4. **Database Changes**: Run migrations in container

### Testing Changes

1. **Local Testing**: Test in local development environment
2. **Docker Testing**: Test in Docker environment
3. **Widget Testing**: Test widgets on both environments
4. **API Testing**: Verify API endpoints work correctly

## Production Deployment

### Container Strategy
- Docker environment closely mirrors production
- Same Python version, dependencies, and configuration
- Nginx configuration suitable for production use
- Environment variables for production settings

### Key Considerations
- **Database**: Use managed PostgreSQL service in production
- **Static Files**: Use CDN or object storage for static assets
- **Environment Variables**: Configure production-specific settings
- **Monitoring**: Add application and infrastructure monitoring

## Security Notes

### Private PyPI Access
- Repository credentials are embedded in Dockerfile
- Consider using build secrets for production builds
- Rotate credentials regularly

### Container Security
- Base image is regularly updated Amazon Linux
- System packages are updated during build
- Consider security scanning for production images

## Maintenance

### Regular Updates
1. **Update base image**: Rebuild with latest Amazon Linux
2. **Update Python**: Upgrade pyenv Python version
3. **Update dependencies**: Run `poetry update` and test
4. **Update WS packages**: Publish new versions to private PyPI

### Monitoring
- **Container Health**: Monitor container resource usage
- **Application Health**: Use Django health checks
- **Dependency Security**: Scan for vulnerable packages
