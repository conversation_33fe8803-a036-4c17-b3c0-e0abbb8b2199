# Finder-v2 Widget – Iframe Height Auto-Adjustment Guide

Last Modified: 2025-06-24 05:10 UTC+6

## Overview

The finder-v2 widget is usually embedded in third-party pages through an `<iframe>` generated by the widget portal.  Because the widget's height varies (search form, dropdowns, results table, etc.) we rely on **iframeResizer** to grow/shrink the iframe automatically.  This document explains how the integration works after the June 2025 fixes.

## Technology Stack

* **iframeResizer 4.x** – two-part library:
  * **content-window helper** (lives inside iframe) – `static/finder_v2/js/libs/iframeResizer.contentWindow.min.js`
  * **host-page script** (lives in parent page) – `static/finder_v2/js/libs/iframeResizer.min.js`
* **Vue 3 components** – dropdowns (Headless-UI `Listbox`) and results table.
* **Django templates** – inject the content-window script into `widgets/finder_v2/iframe/page.html`.

## High-Level Flow

```mermaid
sequenceDiagram
    Browser→>Iframe: load page.html
    page.html→>iframeResizer.contentWindow: initialise
    loop Any DOM change
        Vue Components-->>parentIFrame.size(): request resize
        parent window-->>Iframe: set new height via postMessage
    end
```

## Key Integration Points

| Location | Code | Purpose |
|----------|------|---------|
| `iframe/page.html` | `<script src="…iframeResizer.contentWindow.min.js"></script>` | Bootstraps content-window auto-resize helper. |
| `finder_v2/page.html` (config & demo preview) | `<script src="…iframeResizer.min.js"></script>` + `iFrameResize({...}, previewFrame)` | Initialises parent-side resize for admin/demo preview iframe. |
| `finder_v2/page.html` (config & demo preview) | `<iframe … autoresize="1" scrolling="no">` | Removes fixed height & disables inner scrollbars. |
| `FinderV2Widget.vue` | `data-iframe-height` attribute on root div | Signals tagged-element strategy & allows initial height detection. |
| `src/apps/widgets/finder_v2/app/src/stores/finder.js` | `window.parentIFrame.size()` (after results / clear) | Resize when results block appears or disappears. |
| `src/apps/widgets/finder_v2/app/src/components/CustomSelector.vue` | `triggerResize()` helper | Resize when any `<Listbox>` is opened or closed. Includes a second call +350 ms for transition completion. |

### CustomSelector.vue Details

```12:60:src/apps/widgets/finder_v2/app/src/components/CustomSelector.vue
// ... existing code ...
const triggerResize = () => {
  if (window.parentIFrame && window.parentIFrame.size) {
    window.parentIFrame.size()               // immediate
    setTimeout(() => {
      window.parentIFrame && window.parentIFrame.size() // after 350 ms
    }, 350)
  }
}
```

Why two calls?

1. **Immediate** – accounts for the button taking the focus row.
2. **Delayed (350 ms)** – after Headless-UI finishes the dropdown enter / leave transition so the full menu height is known.

`triggerResize()` is wired to:

* Every manual click on a Listbox button.
* The programmatic click used by the auto-expand feature.
* A watcher on `modelValue` (fires when the dropdown closes after selection).

### store/finder.js Details

* After the search API resolves, the results table is added to the DOM – call `parentIFrame.size()` after `results.value` is updated.
* Clearing results (new search) calls `size()` after 50 ms to shrink back.

## Troubleshooting

1. **Iframe does not grow when dropdown opens**
   * Confirm the dropdown `ListboxOptions` is **not** absolutely positioned (`position: static`/`relative`).
   * Check DevTools console for `parentIFrame is undefined` – usually means the host page removed the iframeResizer initialisation script.
2. **Jumpiness / double scroll**
   * Ensure only one layer of scrollbars – host page must not clip the iframe.
3. **Height updates delayed**
   * Adjust the secondary timeout in `triggerResize()` if Headless-UI transition duration changes.

## Best Practices

* **Never** hard-code a fixed height for the iframe – leave `height` attr empty and rely on iFrameResize.
* Always include **both** parts of iframeResizer (host + content) when embedding widgets outside of portal (e.g. marketing pages).
* Always run `./deploy-finder-v2.sh` after touching JS to update the minified bundle shipped to production.
* Keep `iframeResizer.contentWindow.min.js` version in sync with the parent-side script (served by portal).

## Related Documents

* [finder-v2-knowledge-transfer.md](./finder-v2-knowledge-transfer.md) – complete widget documentation.
* [widget-api-testing-guide.md](../security/widget-api-testing-guide.md) – CSRF & API troubleshooting.

---

© Wheel-Size Services – Internal documentation. 