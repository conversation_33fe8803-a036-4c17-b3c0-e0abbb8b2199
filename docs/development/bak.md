Analyze the finder-v2 widget configuration demo interface at http://development.local:8000/widget/finder-v2/config-demo/ and modernize its styling by replacing all existing HTML/CSS with TailwindCSS v4.1 classes and utilities.

**Specific Requirements:**
1. **Access the config-demo endpoint** - Navigate to the finder-v2 configuration demo interface (requires login/admin permissions)
2. **Identify current styling approach** - Examine existing HTML templates, CSS files, and styling methods used in the finder-v2 widget configuration interface
3. **Replace with TailwindCSS v4.1** - Convert all custom CSS, Bootstrap classes, or other styling frameworks to modern TailwindCSS v4.1 utility classes
4. **Maintain functionality** - Ensure all interactive elements, forms, and JavaScript functionality continue to work after the styling conversion
5. **Follow TailwindCSS best practices** - Use semantic utility combinations, responsive design classes, and modern design patterns
6. **Preserve existing behavior** - Keep the same user experience, form validation, and widget configuration workflow

**Files to examine and potentially modify:**
- Widget configuration templates in `src/templates/widgets/finder_v2/`
- Form templates and styling in finder-v2 configuration views
- Any custom CSS files specific to the config-demo interface
- Django form rendering and widget styling

**Context:** This is part of the systematic TailwindCSS v4.1 adoption for the wheel-size-services project, following the established pattern of comprehensive documentation analysis and systematic upgrades while maintaining 100% backward compatibility.

**Expected outcome:** A modernized, responsive configuration demo interface using TailwindCSS v4.1 that maintains all existing functionality while providing improved visual design and consistency with modern web standards.


# Build CSS for production
cd src/apps/portal && npm run build-css

# Watch for changes during development
cd src/apps/portal && npm run watch-css

# Quick rebuild from project root
./rebuild-portal-css.sh