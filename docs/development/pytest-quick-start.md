# Pytest Quick Start Guide

**For immediate testing of region filtering functionality**

## Immediate Solution (No Changes Required)

You can test the region filtering functionality right now with this command:

```bash
docker compose exec web bash -c "
cd /code && 
export DJANGO_SETTINGS_MODULE=src.settings.dev_docker && 
export PYTHONPATH=/code:/code/src && 
/root/.pyenv/versions/3.12.0/bin/python -c '
import os
import sys
import django
from django.conf import settings

# Minimal Django setup for testing
os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"src.settings.dev_docker\")
django.setup()

# Import and run the test manually
sys.path.insert(0, \"/code\")
from tests.widget.finder_v2.test_region_filtering import RegionParameterNormalisationTest
import unittest

# Create and run test suite
suite = unittest.TestLoader().loadTestsFromTestCase(RegionParameterNormalisationTest)
runner = unittest.TextTestRunner(verbosity=2)
result = runner.run(suite)

print(f\"\\n{'='*50}\")
print(f\"Tests run: {result.testsRun}\")
print(f\"Failures: {len(result.failures)}\")
print(f\"Errors: {len(result.errors)}\")
print(f\"Success: {result.wasSuccessful()}\")
print(f\"{'='*50}\")

exit(0 if result.wasSuccessful() else 1)
'"
```

## Quick Implementation (30 minutes)

### Step 1: Create Test Settings (5 minutes)

Create `src/settings/test.py`:

```python
from .dev_docker import *

# Use in-memory SQLite for faster tests
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# Disable migrations for faster tests
MIGRATION_MODULES = {app: None for app in INSTALLED_APPS}

# Disable logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {'null': {'class': 'logging.NullHandler'}},
    'root': {'handlers': ['null']},
}

# Simple password hasher
PASSWORD_HASHERS = ['django.contrib.auth.hashers.MD5PasswordHasher']
```

### Step 2: Create Test Runner Script (10 minutes)

Create `scripts/test_runner.sh`:

```bash
#!/bin/bash
set -e

echo "🧪 Running tests in Docker..."

export DJANGO_SETTINGS_MODULE=src.settings.test
export PYTHONPATH=/code:/code/src
cd /code

/root/.pyenv/versions/3.12.0/bin/python -m pytest "$@"
```

Make it executable:
```bash
chmod +x scripts/test_runner.sh
```

### Step 3: Create Pytest Config (5 minutes)

Create `pytest.ini`:

```ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = src.settings.test
python_files = test_*.py
addopts = --tb=short --disable-warnings -v
testpaths = tests
```

### Step 4: Install Missing Dependencies (5 minutes)

```bash
docker compose exec web bash -c "
cd /code && 
/root/.pyenv/versions/3.12.0/bin/pip install pytest pytest-django
"
```

### Step 5: Test It! (5 minutes)

```bash
docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py -v
```

## Expected Output

```
🧪 Running tests in Docker...
========================= test session starts ==========================
platform linux -- Python 3.12.0, pytest-7.4.0, pluggy-1.6.0
django: version 4.2.21, settings: src.settings.test
rootdir: /code
configfile: pytest.ini
testpaths: tests
plugins: django-4.5.2
collected 2 items

tests/widget/finder_v2/test_region_filtering.py::RegionParameterNormalisationTest::test_region_array_param_is_normalised_and_overrides_widget_config PASSED [50%]
tests/widget/finder_v2/test_region_filtering.py::RegionParameterNormalisationTest::test_http_request_path PASSED [100%]

========================== 2 passed in 0.12s ==========================
```

## Troubleshooting

### If Django setup fails:
```bash
# Check if all required packages are installed
docker compose exec web bash -c "/root/.pyenv/versions/3.12.0/bin/pip list | grep -E 'django|pytest'"
```

### If imports fail:
```bash
# Verify Python path
docker compose exec web bash -c "cd /code && /root/.pyenv/versions/3.12.0/bin/python -c 'import sys; print(sys.path)'"
```

### If database errors occur:
```bash
# Use the dev_docker settings instead
export DJANGO_SETTINGS_MODULE=src.settings.dev_docker
```

## Next Steps

Once this works, you can implement the full plan from `pytest-docker-integration-plan.md` for a comprehensive testing setup.

## Common Commands

```bash
# Run all tests
docker compose exec web bash /code/scripts/test_runner.sh

# Run specific test file
docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py

# Run with more verbose output
docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py -vv

# Run specific test method
docker compose exec web bash /code/scripts/test_runner.sh tests/widget/finder_v2/test_region_filtering.py::RegionParameterNormalisationTest::test_region_array_param_is_normalised_and_overrides_widget_config
``` 