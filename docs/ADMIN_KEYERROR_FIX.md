# Django Admin KeyError Fix

**Last Modified: 2025-06-17 13:50 UTC+6**

## Issue Description

A Django KeyError exception was occurring in the wheel-size-services project's widget admin interface when accessing widget configuration change pages.

**Error Details:**
- URL: `http://development.local:8000/admin/widgets/widgetconfig/c69dd8b0993b405f930205e8f6a6e97c/change/`
- Exception: `KeyError: 'type'`
- Location: `/code/src/apps/widgets/main/admin.py`, line 111, in `get_form` method
- Context: Django admin change view for widget configuration
- Django Version: 4.2.21
- Python Version: 3.12.0

## Root Cause Analysis

The issue was caused by unsafe access to form fields and object attributes in the Django admin methods:

1. **Primary Issue**: In `get_form` method, the code was trying to access `form.base_fields['type']` without checking if the 'type' field exists in the form's base_fields. When a field is set as readonly, Django might exclude it from the form's base_fields.

2. **Secondary Issues**: Similar unsafe access patterns in `get_readonly_fields` methods where `obj.subscription.is_trial` was accessed without checking if the subscription exists.

## Solution Implemented

### 1. Fixed `get_form` method in `WidgetConfigurationAdmin`

**Before:**
```python
def get_form(self, request, obj=None, **kwargs):
    form = super(WidgetConfigurationAdmin, self).get_form(request, obj, **kwargs)
    
    # Make type readonly for existing finder-v2 widgets
    if obj and obj.type == 'finder-v2':
        form.base_fields['type'].disabled = True  # KeyError here!
        form.base_fields['type'].help_text = _(...)
    return form
```

**After:**
```python
def get_form(self, request, obj=None, **kwargs):
    form = super(WidgetConfigurationAdmin, self).get_form(request, obj, **kwargs)

    # Make type readonly for existing finder-v2 widgets
    # Check if obj exists, has type attribute, and 'type' field exists in base_fields
    if (obj and hasattr(obj, 'type') and obj.type == 'finder-v2' and 
        hasattr(form, 'base_fields') and 'type' in form.base_fields):
        form.base_fields['type'].disabled = True
        form.base_fields['type'].help_text = _(...)
    return form
```

### 2. Fixed `get_readonly_fields` method in `WidgetConfigurationAdmin`

**Before:**
```python
if obj is not None and obj.subscription.is_trial:  # Potential AttributeError
    fields = ['user', 'name'] + fields
```

**After:**
```python
# Check if obj exists and has subscription before accessing subscription attributes
if (obj is not None and hasattr(obj, 'subscription') and 
    hasattr(obj.subscription, 'is_trial') and obj.subscription.is_trial):
    fields = ['user', 'name'] + fields
```

### 3. Fixed `get_readonly_fields` method in `WidgetSubscriptionInline`

**Before:**
```python
if obj is not None and (obj.is_default or obj.subscription.is_trial):
    return [field.name for field in self.model._meta.fields if field.name != 'id']
```

**After:**
```python
# Check if obj exists and has subscription before accessing subscription attributes
if (obj is not None and (obj.is_default or 
    (hasattr(obj, 'subscription') and hasattr(obj.subscription, 'is_trial') and obj.subscription.is_trial))):
    return [field.name for field in self.model._meta.fields if field.name != 'id']
```

### 4. Added missing import

Added the translation import that was missing:
```python
from django.utils.translation import gettext_lazy as _
```

## Files Modified

- `src/apps/widgets/main/admin.py` - Main fix for KeyError issues

## Testing

The fix addresses the following scenarios:

1. **Accessing widget configurations where 'type' field is readonly** - No longer causes KeyError
2. **Widget objects without subscription relationships** - No longer causes AttributeError
3. **Form objects without expected base_fields structure** - Safely handled with hasattr checks
4. **Finder-v2 widgets with proper type field handling** - Type field is properly disabled when available

## Backward Compatibility

✅ **100% backward compatible** - The fix only adds safety checks and doesn't change the existing functionality when all expected attributes are present.

## Deployment Notes

- **No database migrations required**
- **No additional dependencies required**
- **Server restart recommended** to ensure changes are loaded
- **No configuration changes needed**

## Prevention

To prevent similar issues in the future:

1. Always use `hasattr()` checks before accessing object attributes that might not exist
2. Always check if dictionary keys exist before accessing them
3. Use defensive programming practices in Django admin methods
4. Test admin functionality with various widget types and configurations

## Verification

After deployment, verify the fix by:

1. Accessing the problematic URL: `http://development.local:8000/admin/widgets/widgetconfig/c69dd8b0993b405f930205e8f6a6e97c/change/`
2. Testing widget configuration changes for different widget types
3. Verifying that finder-v2 widgets still have the type field properly disabled
4. Checking that readonly field logic still works correctly
