# Finder-v2 Form Data Persistence Fix

**Last Modified: 2025-01-20 15:30 UTC+6**

## Problem Description

The `flow_type` field value in the finder-v2 widget configuration form was not being properly displayed after saving the configuration. Users would:

1. Change the "Interface Flow Type" dropdown from "Primary Flow" to "Alternative Flow"
2. Click "Update Demo Configuration" to save the form
3. See the page reload automatically after successful submission
4. Notice that the form still showed "Primary Flow" as selected, even though the data was saved correctly in the database

## Root Cause Analysis

The issue was caused by <PERSON><PERSON><PERSON>'s `@cached_property` mechanism on the `WidgetConfig.params` property:

1. **Cached Property Issue**: The `WidgetConfig.params` property is decorated with `@cached_property`, which caches the result until the object is garbage collected
2. **Stale Cache After Save**: After form submission and save, the view updates `self.object` to point to the newly saved config, but the `params` property remained cached with old data
3. **Form Initialization with Stale Data**: When the form is re-initialized for the redirect response, it uses the cached (stale) `params` data instead of the fresh database values

## Technical Details

### Before Fix
```python
# In form_valid method
saved_objects = form.save()
if 'config' in saved_objects:
    config, subscription = saved_objects['config']
    self.object = config  # Object updated but params still cached
```

### After Fix
```python
# In form_valid method
saved_objects = form.save()
if 'config' in saved_objects:
    config, subscription = saved_objects['config']
    
    # CRITICAL FIX: Reload from database to clear cached properties
    from src.apps.widgets.common.models import WidgetConfig
    self.object = WidgetConfig.objects.get(uuid=config.uuid)
    
    # Clear any cached properties to ensure fresh data
    if hasattr(self.object, '_params'):
        delattr(self.object, '_params')
    if hasattr(self.object, '_widget_type'):
        delattr(self.object, '_widget_type')
```

## Files Modified

### 1. `src/apps/widgets/main/views/config.py`

**Changes Made**:
- Modified `WidgetConfigView.form_valid()` method (lines 119-129)
- Modified `WidgetDemoConfigView.form_valid()` method (lines 229-238)

**Purpose**: Added database reload and cache invalidation logic to ensure fresh data is used for form initialization after save.

### 2. `tests/widget/finder_v2/test_form_persistence.py` (New File)

**Purpose**: Comprehensive test suite to verify the fix works correctly and prevent regression.

**Test Coverage**:
- Demo configuration form persistence
- Authenticated configuration form persistence  
- Multiple flow type changes
- Database save verification
- Form display verification

## Solution Benefits

1. **Immediate Fix**: Resolves the specific `flow_type` field persistence issue
2. **General Solution**: Fixes the underlying cached property issue for all form fields
3. **Backward Compatible**: No breaking changes to existing functionality
4. **Well Tested**: Comprehensive test coverage to prevent regression
5. **Performance Conscious**: Minimal overhead (one additional database query per form save)

## Testing Instructions

### Manual Testing
1. Navigate to: `http://development.local:8000/widget/fd77b77985314fbab6047e9fa420360d/config-demo/`
2. Change "Interface Flow Type" from "Primary Flow" to "Alternative Flow"
3. Click "Update Demo Configuration"
4. Verify that after page reload, "Alternative Flow" is selected
5. Test changing back to "Primary Flow" to ensure bidirectional functionality

### Automated Testing
```bash
# Run the specific test
python manage.py test tests.widget.finder_v2.test_form_persistence

# Run all finder-v2 tests
python manage.py test tests.widget.finder_v2
```

## Implementation Notes

### Why Database Reload?
- Django's `@cached_property` doesn't automatically invalidate when the underlying data changes
- Simply updating `self.object` doesn't clear the cached `params` property
- Reloading from database ensures we get a fresh object with no cached properties

### Why Clear Cached Properties?
- Extra safety measure to ensure no stale cached data remains
- Handles edge cases where the object might have been partially cached
- Minimal performance impact since these properties will be re-cached on next access

### Alternative Solutions Considered
1. **Manual Cache Invalidation**: Using `del self.object.params` - rejected because it's fragile
2. **Form Re-initialization**: Creating a new form instance - rejected because it's complex
3. **Custom Property Implementation**: Removing `@cached_property` - rejected because it impacts performance

## Future Considerations

This fix addresses a fundamental issue with Django's cached properties in form handling. Consider:

1. **Documentation**: Update developer guidelines about cached property handling in views
2. **Code Review**: Check other views for similar cached property issues
3. **Framework Enhancement**: Consider contributing a Django enhancement for automatic cache invalidation

## Related Issues

This fix may resolve similar issues in other widget types that use cached properties. Monitor for:
- Other form fields not persisting correctly
- Configuration changes not reflecting immediately
- Cached data inconsistencies in widget management

## Verification Checklist

- [x] `flow_type` field persists correctly in demo configuration
- [x] `flow_type` field persists correctly in authenticated configuration  
- [x] Multiple changes work correctly (primary → alternative → primary)
- [x] Database saves are verified
- [x] Form display shows correct values after save
- [x] No regression in other form fields
- [x] Success messages display correctly
- [x] Redirect functionality works as expected
