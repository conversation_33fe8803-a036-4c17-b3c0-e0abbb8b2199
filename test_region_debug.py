#!/usr/bin/env python3
"""
Simple test to demonstrate the region filtering issue.
This script simulates the exact parameter processing that happens in FinderV2WidgetProxyView.
"""

def process_request_params(query_params, widget_config_regions):
    """
    Simulate the exact logic from FinderV2WidgetProxyView.get_request_params()
    """
    params = {}
    
    # Simulate URL parameter processing
    for raw_key, values in query_params.items():
        # Normalize keys that end with [] 
        normalised_key = raw_key[:-2] if raw_key.endswith("[]") else raw_key
        
        if len(values) == 1:
            params[normalised_key] = values[0]
        else:
            params[normalised_key] = values
    
    # Simulate widget config filtering
    filter_params = {"region": widget_config_regions}
    
    # Special handling for region parameters
    if 'region' in filter_params:
        widget_regions = filter_params['region']
        if isinstance(widget_regions, list):
            # Get existing regions from URL parameters
            url_regions = params.get('region', [])
            if not isinstance(url_regions, list):
                url_regions = [url_regions] if url_regions else []
            
            # TEMPORARY FIX: Use URL regions only if provided, otherwise use widget config
            if url_regions:
                merged_regions = url_regions  # Use only URL regions
            else:
                merged_regions = list(widget_regions)  # Fallback to widget config
            
            params['region'] = merged_regions
    
    return params

# Test case: User provides non-Chinese regions in URL
print("=== Region Filtering Debug Test ===\n")

# Simulate the exact scenario the user reported
query_params = {
    "region[]": ["usdm", "cdm"]  # User excludes Chinese regions
}

widget_config_regions = ["chdm"]  # Widget config includes Chinese region

result_params = process_request_params(query_params, widget_config_regions)

print(f"URL regions (user input): {query_params['region[]']}")
print(f"Widget config regions: {widget_config_regions}")
print(f"Final API parameters: {result_params}")

# Check if Chinese region is excluded
final_regions = result_params.get('region', [])
if 'chdm' in final_regions:
    print("\n❌ FAILED: Chinese region 'chdm' is in final parameters")
    print("❌ This would cause Chinese brands like 'Aion' to be returned")
else:
    print("\n✅ PASSED: Chinese region 'chdm' correctly excluded from final parameters")
    print("✅ Only non-Chinese regions will be sent to API")

print(f"\nFinal region parameter that will be sent to wheel-size.com API:")
print(f"region={final_regions}")

print(f"\nExpected API request format:")
print(f"GET /v2/makes/?region=usdm&region=cdm")
print(f"(This should exclude Chinese brands like Aion)")